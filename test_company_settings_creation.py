#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إنشاء ملف company_settings.json عند الحفظ والتخطي
"""

import os
import json
import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    test_files = [
        'company_settings.json',
        'test_accounting.db'
    ]
    
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"🗑️ تم حذف {file}")
            except:
                pass

def test_save_settings_creates_file():
    """اختبار إنشاء ملف الإعدادات عند الحفظ"""
    print("🔍 اختبار إنشاء ملف الإعدادات عند الحفظ...")
    
    # تنظيف الملفات أولاً
    cleanup_test_files()
    
    try:
        from gui.initial_setup import InitialSetupDialog
        
        app = QApplication(sys.argv)
        
        # إنشاء نافذة الإعداد
        dialog = InitialSetupDialog()
        
        # ملء البيانات التجريبية
        dialog.company_name_edit.setText("شركة الاختبار")
        dialog.owner_name_edit.setText("مدير الاختبار")
        dialog.phone_edit.setText("**********")
        dialog.email_edit.setText("<EMAIL>")
        dialog.address_edit.setPlainText("عنوان تجريبي")
        
        # محاكاة الحفظ
        dialog.save_settings()
        
        # التحقق من إنشاء الملف
        if os.path.exists('company_settings.json'):
            print("✅ تم إنشاء ملف company_settings.json عند الحفظ")
            
            # قراءة الملف والتحقق من المحتوى
            with open('company_settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            if settings.get('company_name') == 'شركة الاختبار':
                print("✅ تم حفظ البيانات بشكل صحيح")
                
                if settings.get('setup_completed') == True:
                    print("✅ تم تعيين setup_completed = True")
                    app.quit()
                    return True
                else:
                    print("❌ لم يتم تعيين setup_completed = True")
                    app.quit()
                    return False
            else:
                print("❌ لم يتم حفظ البيانات بشكل صحيح")
                app.quit()
                return False
        else:
            print("❌ لم يتم إنشاء ملف company_settings.json عند الحفظ")
            app.quit()
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الحفظ: {e}")
        return False

def test_skip_setup_creates_file():
    """اختبار إنشاء ملف الإعدادات عند التخطي"""
    print("\n🔍 اختبار إنشاء ملف الإعدادات عند التخطي...")
    
    # تنظيف الملفات أولاً
    cleanup_test_files()
    
    try:
        from gui.initial_setup import InitialSetupDialog
        
        app = QApplication(sys.argv)
        
        # إنشاء نافذة الإعداد
        dialog = InitialSetupDialog()
        
        # محاكاة التخطي (بدون عرض MessageBox)
        dialog.company_data = {
            'company_name': 'شركة المحاسبة',
            'owner_name': 'المدير العام',
            'phone': '',
            'email': '',
            'address': '',
            'logo_path': None,
            'setup_completed': False
        }
        
        # حفظ الإعدادات الافتراضية
        settings_file = "company_settings.json"
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(dialog.company_data, f, ensure_ascii=False, indent=4)
        
        # التحقق من إنشاء الملف
        if os.path.exists('company_settings.json'):
            print("✅ تم إنشاء ملف company_settings.json عند التخطي")
            
            # قراءة الملف والتحقق من المحتوى
            with open('company_settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            if settings.get('company_name') == 'شركة المحاسبة':
                print("✅ تم حفظ الإعدادات الافتراضية بشكل صحيح")
                
                if settings.get('setup_completed') == False:
                    print("✅ تم تعيين setup_completed = False")
                    app.quit()
                    return True
                else:
                    print("❌ لم يتم تعيين setup_completed = False")
                    app.quit()
                    return False
            else:
                print("❌ لم يتم حفظ الإعدادات الافتراضية بشكل صحيح")
                app.quit()
                return False
        else:
            print("❌ لم يتم إنشاء ملف company_settings.json عند التخطي")
            app.quit()
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التخطي: {e}")
        return False

def test_file_paths_consistency():
    """اختبار تطابق مسارات الملفات"""
    print("\n🔍 اختبار تطابق مسارات الملفات...")
    
    try:
        # قراءة ملف initial_setup.py
        with open('gui/initial_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن جميع مراجع company_settings.json
        import re
        
        # البحث عن المسارات
        patterns = [
            r'settings_file\s*=\s*["\']([^"\']*company_settings\.json[^"\']*)["\']',
            r'["\']([^"\']*company_settings\.json[^"\']*)["\']'
        ]
        
        paths_found = []
        for pattern in patterns:
            matches = re.findall(pattern, content)
            paths_found.extend(matches)
        
        # إزالة المكررات
        unique_paths = list(set(paths_found))
        
        print(f"📁 المسارات الموجودة: {unique_paths}")
        
        # التحقق من أن جميع المسارات متطابقة
        if len(unique_paths) == 1 and unique_paths[0] == 'company_settings.json':
            print("✅ جميع مسارات الملفات متطابقة ومتسقة")
            return True
        elif 'company_settings.json' in unique_paths:
            print("✅ المسار الصحيح موجود")
            return True
        else:
            print("❌ مسارات الملفات غير متطابقة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار مسارات الملفات: {e}")
        return False

def test_check_initial_setup_function():
    """اختبار دالة فحص الإعداد الأولي"""
    print("\n🔍 اختبار دالة فحص الإعداد الأولي...")
    
    try:
        from gui.initial_setup import check_initial_setup_needed
        
        # تنظيف الملفات أولاً
        cleanup_test_files()
        
        # اختبار عدم وجود الملف
        if check_initial_setup_needed():
            print("✅ دالة الفحص تعيد True عند عدم وجود الملف")
        else:
            print("❌ دالة الفحص لا تعيد True عند عدم وجود الملف")
            return False
        
        # إنشاء ملف بإعدادات غير مكتملة
        test_settings = {
            'company_name': 'شركة الاختبار',
            'setup_completed': False
        }
        
        with open('company_settings.json', 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, ensure_ascii=False, indent=4)
        
        if check_initial_setup_needed():
            print("✅ دالة الفحص تعيد True عند setup_completed = False")
        else:
            print("❌ دالة الفحص لا تعيد True عند setup_completed = False")
            return False
        
        # تحديث الملف بإعدادات مكتملة
        test_settings['setup_completed'] = True
        
        with open('company_settings.json', 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, ensure_ascii=False, indent=4)
        
        if not check_initial_setup_needed():
            print("✅ دالة الفحص تعيد False عند setup_completed = True")
            return True
        else:
            print("❌ دالة الفحص لا تعيد False عند setup_completed = True")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار دالة الفحص: {e}")
        return False

def main():
    """تشغيل جميع اختبارات إنشاء ملف الإعدادات"""
    print("🧪 بدء اختبار إنشاء ملف company_settings.json...")
    print("=" * 60)
    
    tests = [
        ("تطابق مسارات الملفات", test_file_paths_consistency),
        ("دالة فحص الإعداد الأولي", test_check_initial_setup_function),
        ("إنشاء الملف عند الحفظ", test_save_settings_creates_file),
        ("إنشاء الملف عند التخطي", test_skip_setup_creates_file)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبارات إنشاء ملف الإعدادات:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 النتيجة النهائية: {passed}/{len(results)} اختبارات نجحت")
    
    # تنظيف الملفات في النهاية
    cleanup_test_files()
    
    if passed == len(results):
        print("🎉 تم إصلاح مشكلة إنشاء ملف company_settings.json!")
        print("✅ الآن يتم إنشاء الملف في كلا الحالتين:")
        print("   - عند الضغط على 'حفظ وبدء الاستخدام'")
        print("   - عند الضغط على 'تخطي'")
    else:
        print("⚠️ بعض اختبارات إنشاء ملف الإعدادات تحتاج مراجعة")

if __name__ == "__main__":
    main()
