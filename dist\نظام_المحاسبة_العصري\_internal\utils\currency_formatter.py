#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مساعد تنسيق العملة والأرقام
يستخدم إعدادات التطبيق لتنسيق الأرقام والعملة
"""

from utils.settings_manager import get_settings_manager

def format_currency(amount, show_symbol=True, engine=None):
    """
    تنسيق المبلغ بالعملة الحالية
    
    Args:
        amount: المبلغ المراد تنسيقه
        show_symbol: إظهار رمز العملة أم لا
        engine: محرك قاعدة البيانات (اختياري)
    
    Returns:
        str: المبلغ منسق
    """
    try:
        if engine:
            settings_manager = get_settings_manager(engine)
        else:
            # استخدام الإعدادات الافتراضية
            settings_manager = None
        
        if settings_manager:
            return settings_manager.format_currency(amount)
        else:
            # تنسيق افتراضي للجنيه المصري بدون خانات عشرية
            formatted_amount = f"{amount:,.0f}"
            if show_symbol:
                return f"{formatted_amount} ج.م"
            else:
                return formatted_amount
                
    except Exception as e:
        print(f"خطأ في تنسيق العملة: {e}")
        # تنسيق افتراضي في حالة الخطأ
        formatted_amount = f"{amount:,.0f}"
        if show_symbol:
            return f"{formatted_amount} ج.م"
        else:
            return formatted_amount

def format_number(amount, decimal_places=None):
    """
    تنسيق الرقم بدون رمز العملة
    
    Args:
        amount: الرقم المراد تنسيقه
        decimal_places: عدد الخانات العشرية (اختياري)
    
    Returns:
        str: الرقم منسق
    """
    try:
        if decimal_places is None:
            # استخدام الإعدادات الافتراضية (0 خانات عشرية)
            decimal_places = 0
        
        return f"{amount:,.{decimal_places}f}"
        
    except Exception as e:
        print(f"خطأ في تنسيق الرقم: {e}")
        return f"{amount:,.0f}"

def get_currency_symbol():
    """الحصول على رمز العملة الحالي"""
    return "ج.م"

def get_decimal_places():
    """الحصول على عدد الخانات العشرية"""
    return 0

# دوال مساعدة للتوافق مع الكود القديم
def format_amount(amount):
    """تنسيق المبلغ بدون رمز العملة"""
    return format_number(amount, 0)

def format_price(price):
    """تنسيق السعر مع رمز العملة"""
    return format_currency(price, True)

def format_total(total):
    """تنسيق الإجمالي مع رمز العملة"""
    return format_currency(total, True)
