from sqlalchemy.orm import Session
from sqlalchemy import desc
from database.users import AuditLog
from datetime import datetime
from functools import wraps

class AuditSystem:
    def __init__(self, engine):
        self.engine = engine

    def log_action(self, user_id, action, table_name=None, record_id=None, details=None):
        """تسجيل حدث في سجل التدقيق"""
        try:
            with Session(self.engine) as session:
                log = AuditLog(
                    user_id=user_id,
                    action=action,
                    table_name=table_name,
                    record_id=record_id,
                    details=details,
                    timestamp=datetime.now()
                )
                session.add(log)
                session.commit()
        except Exception as e:
            print(f"خطأ في تسجيل الحدث: {str(e)}")

    def get_user_actions(self, user_id, limit=100):
        """الحصول على سجل عمليات مستخدم معين"""
        with Session(self.engine) as session:
            return session.query(AuditLog).filter(
                AuditLog.user_id == user_id
            ).order_by(desc(AuditLog.timestamp)).limit(limit).all()

    def get_table_actions(self, table_name, limit=100):
        """الحصول على سجل العمليات على جدول معين"""
        with Session(self.engine) as session:
            return session.query(AuditLog).filter(
                AuditLog.table_name == table_name
            ).order_by(desc(AuditLog.timestamp)).limit(limit).all()

    def get_recent_actions(self, limit=100):
        """الحصول على أحدث العمليات في النظام"""
        with Session(self.engine) as session:
            return session.query(AuditLog).order_by(
                desc(AuditLog.timestamp)
            ).limit(limit).all()

class ActionLogger:
    """مُسجل العمليات باستخدام سياق with"""
    def __init__(self, audit_system, user_id, action, table_name=None):
        self.audit_system = audit_system
        self.user_id = user_id
        self.action = action
        self.table_name = table_name
        self.details = []
        self.success = False

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            self.success = True
        self.audit_system.log_action(
            self.user_id,
            self.action,
            self.table_name,
            details="; ".join(self.details) if self.details else None
        )
        return False  # ترك معالجة الاستثناءات للمستدعي

    def add_detail(self, detail):
        """إضافة تفاصيل للعملية"""
        self.details.append(detail)

def audit_action(action, table_name=None):
    """مزخرف (decorator) لتسجيل العمليات تلقائياً"""
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            if not hasattr(self, 'audit_system') or not hasattr(self, 'current_user'):
                return func(self, *args, **kwargs)

            with ActionLogger(self.audit_system, self.current_user.id, action, table_name) as logger:
                result = func(self, *args, **kwargs)
                if isinstance(result, dict):
                    logger.add_detail(str(result))
                return result
        return wrapper
    return decorator