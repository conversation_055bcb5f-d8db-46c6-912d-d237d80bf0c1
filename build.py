import PyInstaller.__main__
import os
import shutil

def build_app():
    # تنظيف المجلدات القديمة
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')

    # إعداد خيارات PyInstaller
    options = [
        'main.py',  # الملف الرئيسي
        '--name=نظام_المحاسبة_العصري',  # اسم البرنامج
        '--windowed',  # تشغيل بدون نافذة الكونسول
        '--onefile',  # تجميع كل شيء في ملف واحد
        '--icon=assets/icons.ico',  # أيقونة البرنامج
        '--add-data=assets;assets',  # إضافة مجلد الأصول
        '--add-data=company_settings.json;.',  # إضافة ملف الإعدادات
        '--add-data=accounting.db;.',  # إضافة ملف قاعدة البيانات
        '--add-data=license.dat;.',  # إضافة ملف ترخيص
        '--add-data=gui;gui',  # إضافة مجلد الواجهة الرسومية
        '--hidden-import=werkzeug',  # تضمين werkzeug
        '--hidden-import=PyQt5.QtChart',
        '--hidden-import=seaborn',
        '--collect-all=sqlalchemy',
        '--clean',  # تنظيف الملفات المؤقتة
        '--noconfirm',  # عدم طلب تأكيد
    ]

    # بناء البرنامج
    PyInstaller.__main__.run(options)

    print("تم بناء البرنامج بنجاح!")
    print("يمكنك العثور على الملف التنفيذي في مجلد 'dist'")

if __name__ == '__main__':
    build_app()