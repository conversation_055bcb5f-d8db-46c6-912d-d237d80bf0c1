#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء فوري ومبسط
"""

import subprocess
import sys
import os

def main():
    print("🚀 بناء فوري من main.py...")
    
    # التحقق من وجود main.py
    if not os.path.exists('main.py'):
        print("❌ main.py غير موجود!")
        return False
    
    print("✅ main.py موجود")
    
    # أمر البناء المبسط
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name", "نظام_المحاسبة_العصري_v2_فوري",
        "main.py"
    ]
    
    print("🔨 بدء البناء...")
    print(f"الأمر: {' '.join(cmd)}")
    
    try:
        # تشغيل الأمر
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ تم البناء بنجاح!")
        
        # التحقق من الملف الناتج
        exe_path = "dist/نظام_المحاسبة_العصري_v2_فوري.exe"
        if os.path.exists(exe_path):
            print(f"✅ الملف التنفيذي: {exe_path}")
            
            # نسخ إلى المجلد الحالي
            import shutil
            shutil.copy2(exe_path, ".")
            print("✅ تم نسخ الملف للمجلد الحالي")
            
            return True
        else:
            print("❌ لم يتم العثور على الملف التنفيذي")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل البناء: {e}")
        if e.stdout:
            print(f"المخرجات: {e.stdout}")
        if e.stderr:
            print(f"الأخطاء: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    if main():
        print("\n🎉 البناء مكتمل!")
    else:
        print("\n❌ فشل البناء!")
    
    input("اضغط Enter للخروج...")
