#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات فواتير المشتريات
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def test_purchases_save_and_print():
    """اختبار دالة حفظ وطباعة في فاتورة المشتريات"""
    print("🔍 اختبار دالة حفظ وطباعة في فاتورة المشتريات...")
    
    try:
        from gui.purchases import PurchasesWidget
        
        # التحقق من وجود الدالة
        if hasattr(PurchasesWidget, 'save_and_print'):
            print("✅ دالة save_and_print موجودة")
            
            # التحقق من وجود دالة print_invoice
            if hasattr(PurchasesWidget, 'print_invoice'):
                print("✅ دالة print_invoice موجودة")
                return True
            else:
                print("❌ دالة print_invoice غير موجودة")
                return False
        else:
            print("❌ دالة save_and_print غير موجودة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال المشتريات: {e}")
        return False

def test_purchase_invoices_view_print_button():
    """اختبار زر الطباعة في صفحة عرض فواتير المشتريات"""
    print("\n🔍 اختبار زر الطباعة في صفحة عرض فواتير المشتريات...")
    
    try:
        from gui.purchase_invoices_view import PurchaseInvoicesViewWidget, PurchaseInvoiceDetailsDialog
        
        # التحقق من وجود دالة الطباعة في صفحة العرض
        if hasattr(PurchaseInvoicesViewWidget, 'print_invoice'):
            print("✅ دالة print_invoice موجودة في صفحة العرض")
        else:
            print("❌ دالة print_invoice غير موجودة في صفحة العرض")
            return False
        
        # التحقق من وجود دالة الطباعة في نافذة التفاصيل
        if hasattr(PurchaseInvoiceDetailsDialog, 'print_invoice'):
            print("✅ دالة print_invoice موجودة في نافذة التفاصيل")
        else:
            print("❌ دالة print_invoice غير موجودة في نافذة التفاصيل")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار صفحة عرض فواتير المشتريات: {e}")
        return False

def test_print_dialog_import():
    """اختبار استيراد نافذة الطباعة"""
    print("\n🔍 اختبار استيراد نافذة الطباعة...")
    
    try:
        from utils.new_design_invoice_printer import show_new_design_print_dialog
        print("✅ تم استيراد نافذة الطباعة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد نافذة الطباعة: {e}")
        return False

def test_purchases_widget_creation():
    """اختبار إنشاء واجهة المشتريات"""
    print("\n🔍 اختبار إنشاء واجهة المشتريات...")
    
    try:
        from sqlalchemy import create_engine
        from database.users import init_db
        
        # إنشاء قاعدة بيانات مؤقتة للاختبار
        engine = create_engine('sqlite:///test_purchases.db', echo=False)
        init_db(engine)
        
        app = QApplication(sys.argv)
        
        from gui.purchases import PurchasesWidget
        
        # إنشاء واجهة المشتريات
        purchases_widget = PurchasesWidget(engine)
        
        # التحقق من وجود الأزرار
        if hasattr(purchases_widget, 'save_and_print'):
            print("✅ تم إنشاء واجهة المشتريات بنجاح")
            
            # إغلاق التطبيق
            app.quit()
            
            # حذف قاعدة البيانات المؤقتة
            if os.path.exists('test_purchases.db'):
                os.remove('test_purchases.db')
            
            return True
        else:
            print("❌ فشل في إنشاء واجهة المشتريات")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء واجهة المشتريات: {e}")
        return False

def test_code_syntax():
    """اختبار صحة الكود النحوية"""
    print("\n🔍 اختبار صحة الكود النحوية...")
    
    files_to_test = [
        'gui/purchases.py',
        'gui/purchase_invoices_view.py'
    ]
    
    all_good = True
    
    for file_path in files_to_test:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            # محاولة تجميع الكود للتحقق من صحته النحوية
            compile(code, file_path, 'exec')
            print(f"✅ {file_path}: صحيح نحوياً")
            
        except SyntaxError as e:
            print(f"❌ {file_path}: خطأ نحوي - {e}")
            all_good = False
        except Exception as e:
            print(f"⚠️ {file_path}: تحذير - {e}")
    
    return all_good

def test_function_calls():
    """اختبار استدعاءات الدوال"""
    print("\n🔍 اختبار استدعاءات الدوال...")
    
    try:
        # قراءة ملف المشتريات
        with open('gui/purchases.py', 'r', encoding='utf-8') as f:
            purchases_code = f.read()
        
        # التحقق من الاستدعاء الصحيح للطباعة
        if 'self.print_invoice(invoice_id)' in purchases_code:
            print("✅ استدعاء دالة الطباعة صحيح في المشتريات")
        else:
            print("❌ استدعاء دالة الطباعة غير صحيح في المشتريات")
            return False
        
        # قراءة ملف عرض فواتير المشتريات
        with open('gui/purchase_invoices_view.py', 'r', encoding='utf-8') as f:
            view_code = f.read()
        
        # التحقق من وجود أزرار الطباعة
        if '🖨️ طباعة' in view_code:
            print("✅ أزرار الطباعة موجودة في صفحة العرض")
        else:
            print("❌ أزرار الطباعة غير موجودة في صفحة العرض")
            return False
        
        # التحقق من استدعاء دالة الطباعة
        if 'show_new_design_print_dialog' in view_code:
            print("✅ استدعاء نافذة الطباعة موجود")
        else:
            print("❌ استدعاء نافذة الطباعة غير موجود")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار استدعاءات الدوال: {e}")
        return False

def main():
    """تشغيل جميع اختبارات إصلاحات المشتريات"""
    print("🧪 بدء اختبار إصلاحات فواتير المشتريات...")
    print("=" * 60)
    
    tests = [
        ("صحة الكود النحوية", test_code_syntax),
        ("دوال المشتريات", test_purchases_save_and_print),
        ("أزرار الطباعة في العرض", test_purchase_invoices_view_print_button),
        ("استيراد نافذة الطباعة", test_print_dialog_import),
        ("استدعاءات الدوال", test_function_calls),
        ("إنشاء واجهة المشتريات", test_purchases_widget_creation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبارات إصلاحات المشتريات:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 النتيجة النهائية: {passed}/{len(results)} اختبارات نجحت")
    
    if passed == len(results):
        print("🎉 جميع إصلاحات المشتريات تعمل بشكل صحيح!")
        print("✅ الإصلاحات المطبقة:")
        print("   - إصلاح دالة حفظ وطباعة في فاتورة المشتريات")
        print("   - إضافة زر الطباعة في صفحة عرض الفواتير")
        print("   - إضافة زر الطباعة في نافذة تفاصيل الفاتورة")
        print("   - ربط جميع الأزرار بنظام الطباعة المحسن")
    else:
        print("⚠️ بعض إصلاحات المشتريات تحتاج مراجعة")

if __name__ == "__main__":
    main()
