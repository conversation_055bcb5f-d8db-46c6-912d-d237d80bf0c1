"""
نظام الألوان والتصميم العصري للبرنامج
مستوحى من أفضل البرامج الحديثة مثل Slack, Discord, VS Code
"""

# ألوان النظام الحديث
COLORS = {
    # الألوان الأساسية
    'primary': '#6366F1',           # بنفسجي عصري (Indigo)
    'primary_dark': '#4F46E5',      # بنفسجي غامق
    'primary_light': '#8B5CF6',     # بنفسجي فاتح
    
    # الألوان الثانوية
    'secondary': '#10B981',         # أخضر عصري (Emerald)
    'secondary_dark': '#059669',    # أخضر غامق
    'secondary_light': '#34D399',   # أخضر فاتح
    
    # ألوان الخلفية
    'background': '#0F172A',        # خلفية داكنة عصرية
    'surface': '#1E293B',           # سطح داكن
    'surface_light': '#334155',     # سطح فاتح
    'card': '#1E293B',              # خلفية الكروت
    'sidebar': '#0F172A',           # خلفية الشريط الجانبي
    
    # ألوان النصوص
    'text_primary': '#F8FAFC',      # نص أساسي أبيض
    'text_secondary': '#CBD5E1',    # نص ثانوي رمادي فاتح
    'text_muted': '#94A3B8',        # نص خافت
    'text_dark': '#1E293B',         # نص داكن للخلفيات الفاتحة
    
    # ألوان الحالة
    'success': '#10B981',           # نجاح - أخضر
    'warning': '#F59E0B',           # تحذير - برتقالي
    'error': '#EF4444',             # خطأ - أحمر
    'info': '#3B82F6',              # معلومات - أزرق
    
    # ألوان الحدود
    'border': '#334155',            # حدود عادية
    'border_light': '#475569',      # حدود فاتحة
    'border_focus': '#6366F1',      # حدود عند التركيز
    
    # ألوان التدرج
    'gradient_start': '#6366F1',    # بداية التدرج
    'gradient_end': '#8B5CF6',      # نهاية التدرج
    
    # ألوان شفافة
    'overlay': 'rgba(15, 23, 42, 0.8)',        # طبقة شفافة
    'glass': 'rgba(30, 41, 59, 0.7)',          # تأثير الزجاج
    'hover': 'rgba(99, 102, 241, 0.1)',        # تأثير التمرير
}

# أحجام الخطوط العصرية
FONTS = {
    'family': 'Segoe UI, system-ui, -apple-system, sans-serif',
    'sizes': {
        'xs': '12px',
        'sm': '14px',
        'base': '16px',
        'lg': '18px',
        'xl': '20px',
        '2xl': '24px',
        '3xl': '30px',
        '4xl': '36px',
        '5xl': '48px',
    },
    'weights': {
        'light': 300,
        'normal': 400,
        'medium': 500,
        'semibold': 600,
        'bold': 700,
        'extrabold': 800,
    }
}

# المسافات والأبعاد
SPACING = {
    'xs': '4px',
    'sm': '8px',
    'md': '16px',
    'lg': '24px',
    'xl': '32px',
    '2xl': '48px',
    '3xl': '64px',
}

# نصف أقطار الحدود
RADIUS = {
    'none': '0px',
    'sm': '4px',
    'md': '8px',
    'lg': '12px',
    'xl': '16px',
    '2xl': '24px',
    'full': '9999px',
}

# الظلال العصرية
SHADOWS = {
    'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    'inner': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    'glow': '0 0 20px rgba(99, 102, 241, 0.3)',
}

def get_modern_button_style(variant='primary', size='md'):
    """إنشاء ستايل عصري للأزرار"""
    base_style = f"""
        QPushButton {{
            font-family: {FONTS['family']};
            font-weight: {FONTS['weights']['medium']};
            border: none;
            border-radius: {RADIUS['md']};
            padding: {SPACING['sm']} {SPACING['md']};
            transition: all 0.2s ease;
        }}
        QPushButton:hover {{
            transform: translateY(-1px);
            box-shadow: {SHADOWS['lg']};
        }}
        QPushButton:pressed {{
            transform: translateY(0px);
        }}
        QPushButton:disabled {{
            opacity: 0.5;
            cursor: not-allowed;
        }}
    """
    
    if variant == 'primary':
        return base_style + f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 {COLORS['primary']}, stop:1 {COLORS['primary_light']});
                color: {COLORS['text_primary']};
                font-size: {FONTS['sizes']['base']};
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 {COLORS['primary_dark']}, stop:1 {COLORS['primary']});
            }}
        """
    elif variant == 'secondary':
        return base_style + f"""
            QPushButton {{
                background: {COLORS['surface']};
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['border']};
                font-size: {FONTS['sizes']['base']};
            }}
            QPushButton:hover {{
                background: {COLORS['surface_light']};
                border-color: {COLORS['border_light']};
            }}
        """
    elif variant == 'success':
        return base_style + f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 {COLORS['secondary']}, stop:1 {COLORS['secondary_light']});
                color: {COLORS['text_primary']};
                font-size: {FONTS['sizes']['base']};
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 {COLORS['secondary_dark']}, stop:1 {COLORS['secondary']});
            }}
        """
    elif variant == 'danger':
        return base_style + f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 {COLORS['error']}, stop:1 #F87171);
                color: {COLORS['text_primary']};
                font-size: {FONTS['sizes']['base']};
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #DC2626, stop:1 {COLORS['error']});
            }}
        """

def get_modern_input_style():
    """ستايل عصري للحقول"""
    return f"""
        QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            font-family: {FONTS['family']};
            font-size: {FONTS['sizes']['base']};
            background: {COLORS['surface']};
            color: {COLORS['text_primary']};
            border: 1px solid {COLORS['border']};
            border-radius: {RADIUS['md']};
            padding: {SPACING['sm']} {SPACING['md']};
            selection-background-color: {COLORS['primary']};
        }}
        QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
            border-color: {COLORS['border_focus']};
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }}
        QLineEdit:hover, QTextEdit:hover, QSpinBox:hover, QDoubleSpinBox:hover, QComboBox:hover {{
            border-color: {COLORS['border_light']};
        }}
        QComboBox::drop-down {{
            border: none;
            background: transparent;
        }}
        QComboBox::down-arrow {{
            image: none;
            border: none;
            width: 12px;
            height: 12px;
        }}
    """

def get_modern_card_style():
    """ستايل عصري للكروت"""
    return f"""
        QFrame {{
            background: {COLORS['card']};
            border: 1px solid {COLORS['border']};
            border-radius: {RADIUS['lg']};
            padding: {SPACING['lg']};
        }}
        QFrame:hover {{
            border-color: {COLORS['border_light']};
            box-shadow: {SHADOWS['md']};
        }}
    """

def get_modern_table_style():
    """ستايل عصري للجداول"""
    return f"""
        QTableWidget {{
            background: {COLORS['surface']};
            color: {COLORS['text_primary']};
            border: 1px solid {COLORS['border']};
            border-radius: {RADIUS['lg']};
            gridline-color: {COLORS['border']};
            font-family: {FONTS['family']};
            font-size: {FONTS['sizes']['sm']};
        }}
        QTableWidget::item {{
            padding: {SPACING['md']};
            border-bottom: 1px solid {COLORS['border']};
        }}
        QTableWidget::item:selected {{
            background: {COLORS['hover']};
            color: {COLORS['text_primary']};
        }}
        QTableWidget::item:hover {{
            background: rgba(99, 102, 241, 0.05);
        }}
        QHeaderView::section {{
            background: {COLORS['surface_light']};
            color: {COLORS['text_primary']};
            padding: {SPACING['md']};
            border: none;
            border-bottom: 2px solid {COLORS['primary']};
            font-weight: {FONTS['weights']['semibold']};
            font-size: {FONTS['sizes']['sm']};
        }}
        QHeaderView::section:hover {{
            background: {COLORS['border_light']};
        }}
    """

def get_modern_sidebar_style():
    """ستايل عصري للشريط الجانبي"""
    return f"""
        QWidget {{
            background: {COLORS['sidebar']};
            border-right: 1px solid {COLORS['border']};
        }}
        QPushButton {{
            background: transparent;
            color: {COLORS['text_secondary']};
            border: none;
            border-radius: {RADIUS['md']};
            padding: {SPACING['md']};
            text-align: left;
            font-family: {FONTS['family']};
            font-size: {FONTS['sizes']['base']};
            font-weight: {FONTS['weights']['medium']};
            margin: {SPACING['xs']};
        }}
        QPushButton:hover {{
            background: {COLORS['hover']};
            color: {COLORS['text_primary']};
        }}
        QPushButton:checked {{
            background: {COLORS['primary']};
            color: {COLORS['text_primary']};
            box-shadow: {SHADOWS['glow']};
        }}
    """

def get_modern_label_style(variant='primary'):
    """ستايل عصري للتسميات"""
    if variant == 'title':
        return f"""
            QLabel {{
                color: {COLORS['text_primary']};
                font-family: {FONTS['family']};
                font-size: {FONTS['sizes']['2xl']};
                font-weight: {FONTS['weights']['bold']};
                margin-bottom: {SPACING['md']};
            }}
        """
    elif variant == 'subtitle':
        return f"""
            QLabel {{
                color: {COLORS['text_secondary']};
                font-family: {FONTS['family']};
                font-size: {FONTS['sizes']['lg']};
                font-weight: {FONTS['weights']['medium']};
                margin-bottom: {SPACING['sm']};
            }}
        """
    else:
        return f"""
            QLabel {{
                color: {COLORS['text_primary']};
                font-family: {FONTS['family']};
                font-size: {FONTS['sizes']['base']};
                font-weight: {FONTS['weights']['normal']};
            }}
        """

def get_modern_scrollbar_style():
    """ستايل عصري لشريط التمرير"""
    return f"""
        QScrollBar:vertical {{
            background: {COLORS['surface']};
            width: 12px;
            border-radius: 6px;
            margin: 0;
        }}
        QScrollBar::handle:vertical {{
            background: {COLORS['border_light']};
            border-radius: 6px;
            min-height: 20px;
        }}
        QScrollBar::handle:vertical:hover {{
            background: {COLORS['primary']};
        }}
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        QScrollBar:horizontal {{
            background: {COLORS['surface']};
            height: 12px;
            border-radius: 6px;
            margin: 0;
        }}
        QScrollBar::handle:horizontal {{
            background: {COLORS['border_light']};
            border-radius: 6px;
            min-width: 20px;
        }}
        QScrollBar::handle:horizontal:hover {{
            background: {COLORS['primary']};
        }}
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            width: 0px;
        }}
    """
