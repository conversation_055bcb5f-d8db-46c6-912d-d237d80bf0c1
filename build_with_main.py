#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء النسخة النهائية باستخدام main.py الأصلي
مع جميع الإصلاحات والتحسينات المطبقة
"""

import os
import sys
import shutil
import subprocess
import json
from datetime import datetime


def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 80)
    print("🚀 بناء النسخة النهائية - باستخدام main.py الأصلي")
    print("=" * 80)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🏢 المطور: Sico Company")
    print("📧 الدعم: <EMAIL>")
    print("📁 الملف الرئيسي: main.py (مع التصميمات والتقسيمات الأصلية)")
    print("=" * 80)


def verify_main_py():
    """التحقق من وجود وصحة ملف main.py"""
    print("\n🔍 التحقق من ملف main.py...")
    
    if not os.path.exists('main.py'):
        print("❌ ملف main.py غير موجود!")
        return False
    
    # قراءة محتوى الملف للتحقق من الإصلاحات
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من الإصلاحات المهمة
    checks = [
        ("تمرير المستخدم", "MainWindow(engine=engine, user=user)"),
        ("فحص المستخدم", "if hasattr(login_dialog, 'user')"),
        ("طباعة نجاح تسجيل الدخول", "تم تسجيل دخول المستخدم"),
        ("استيراد MainWindow", "from gui.main_window import MainWindow"),
        ("تهيئة قاعدة البيانات", "init_db(engine)")
    ]
    
    all_good = True
    for check_name, check_text in checks:
        if check_text in content:
            print(f"✅ {check_name}: موجود")
        else:
            print(f"❌ {check_name}: غير موجود")
            all_good = False
    
    if all_good:
        print("✅ ملف main.py يحتوي على جميع الإصلاحات المطلوبة!")
    else:
        print("⚠️ ملف main.py قد يحتاج لبعض التحديثات")
    
    return True  # نتابع حتى لو كانت هناك تحذيرات


def check_requirements():
    """فحص المتطلبات المطلوبة"""
    print("\n🔍 فحص المتطلبات...")
    
    required_modules = [
        'PyQt5',
        'sqlalchemy',
        'pyperclip',
        'Pillow',
        'reportlab',
        'openpyxl'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module.lower().replace('-', '_'))
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - غير مثبت")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing_modules)}")
        print("💡 قم بتثبيتها باستخدام: pip install " + " ".join(missing_modules))
        return False
    
    print("✅ جميع المتطلبات متوفرة!")
    return True


def create_version_info():
    """إنشاء معلومات الإصدار"""
    print("\n📋 إنشاء معلومات الإصدار...")
    
    version_info = {
        "version": "2.0.0",
        "build_date": datetime.now().isoformat(),
        "main_file": "main.py",
        "description": "النسخة النهائية باستخدام main.py الأصلي مع جميع الإصلاحات",
        "features": [
            "✅ استخدام main.py الأصلي مع التصميمات والتقسيمات المطلوبة",
            "✅ إصلاح تمرير بيانات المستخدم في الواجهة الرئيسية",
            "✅ إصلاح شاشة التفعيل - نصوص واضحة ومساحات مريحة",
            "✅ إصلاح شاشة إعدادات الشركة - حقول أكبر ونصوص واضحة",
            "✅ حذف الضريبة من فواتير المشتريات",
            "✅ إصلاح مشكلة الطباعة في فواتير المشتريات",
            "✅ إضافة أزرار طباعة في صفحة عرض فواتير المشتريات",
            "✅ تكامل إعدادات الشركة في جميع أجزاء النظام",
            "✅ إصلاح مشكلة إنشاء ملف company_settings.json",
            "✅ إضافة ميزة عرض معلومات الترخيص ورقم الجهاز"
        ],
        "main_py_fixes": [
            "السطر 149-154: إصلاح تمرير بيانات المستخدم للواجهة الرئيسية",
            "السطر 150-152: فحص وجود المستخدم قبل التمرير",
            "السطر 154: تمرير المستخدم بشكل صحيح لـ MainWindow",
            "الحفاظ على التصميم والتقسيمات الأصلية"
        ]
    }
    
    with open("version_info.json", "w", encoding="utf-8") as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)
    
    print("✅ تم إنشاء ملف معلومات الإصدار")


def create_readme():
    """إنشاء ملف README"""
    print("\n📖 إنشاء ملف README...")
    
    readme_content = f"""# 🏢 نظام المحاسبة العصري - الإصدار 2.0

## 📋 وصف البرنامج
نظام محاسبة شامل ومتطور لإدارة المبيعات والمشتريات والمخزون والعملاء.
تم بناؤه باستخدام main.py الأصلي مع جميع التصميمات والتقسيمات المطلوبة.

## ✨ الميزات في الإصدار 2.0

### 🔧 الإصلاحات المطبقة:
- ✅ **main.py محسن** - مع التصميمات والتقسيمات الأصلية
- ✅ **إصلاح تمرير المستخدم** - الواجهة الرئيسية تظهر بنجاح
- ✅ **شاشة التفعيل محسنة** - نصوص واضحة ومساحات مريحة
- ✅ **شاشة إعدادات الشركة محسنة** - حقول أكبر ونصوص واضحة
- ✅ **إصلاح الطباعة** - فواتير المشتريات تطبع بنجاح
- ✅ **إصلاح ملف الإعدادات** - ينشأ في كلا الحالتين

### 🆕 الميزات الجديدة:
- 🔑 **معلومات الترخيص** - عرض رقم الجهاز وكود العميل
- 🖨️ **أزرار طباعة** - في صفحة عرض فواتير المشتريات
- 🏢 **تكامل إعدادات الشركة** - في جميع أجزاء النظام
- 🗑️ **حذف الضريبة** - من فواتير المشتريات
- 📋 **نسخ تلقائي** - لمعلومات الترخيص

## 🚀 طريقة التشغيل
1. قم بتشغيل الملف التنفيذي
2. أدخل بيانات التفعيل أو اختر التخطي
3. أدخل إعدادات شركتك
4. ابدأ استخدام النظام

## 🔑 معلومات الترخيص
- للحصول على معلومات الترخيص: قائمة النظام → معلومات الترخيص
- أو من الصفحة الرئيسية: زر "معلومات الترخيص"

## 📞 الدعم الفني
- 📧 البريد الإلكتروني: <EMAIL>
- 🌐 الموقع: www.sicocompany.com

## 📅 تاريخ الإصدار
{datetime.now().strftime("%Y-%m-%d")}

## 🔧 تفاصيل تقنية
- **الملف الرئيسي:** main.py (مع التصميمات الأصلية)
- **قاعدة البيانات:** SQLite
- **الواجهة:** PyQt5
- **نظام الترخيص:** مدمج

© 2024 Sico Company - جميع الحقوق محفوظة
"""
    
    with open("README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف README")


def build_executable():
    """بناء الملف التنفيذي باستخدام main.py"""
    print("\n🔨 بناء الملف التنفيذي من main.py...")
    
    try:
        # التحقق من وجود PyInstaller
        subprocess.run([sys.executable, "-c", "import PyInstaller"], check=True)
        print("✅ PyInstaller متوفر")
    except subprocess.CalledProcessError:
        print("❌ PyInstaller غير مثبت")
        print("💡 قم بتثبيته: pip install pyinstaller")
        return False
    
    # بناء البرنامج من main.py
    build_command = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed", 
        "--name", "نظام_المحاسبة_العصري_v2_نهائي",
        "--distpath", "dist_final",
        "--workpath", "build_temp",
        "--specpath", ".",
        "main.py"  # استخدام main.py الأصلي
    ]
    
    # إضافة الأيقونة إذا كانت موجودة
    if os.path.exists("assets/icon.ico"):
        build_command.extend(["--icon", "assets/icon.ico"])
    elif os.path.exists("assets/icons.ico"):
        build_command.extend(["--icon", "assets/icons.ico"])
    
    print("🔨 جاري البناء من main.py...")
    print(f"📁 الأمر: {' '.join(build_command)}")
    
    try:
        result = subprocess.run(build_command, check=True, capture_output=True, text=True)
        print("✅ تم بناء الملف التنفيذي بنجاح من main.py!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في البناء: {e}")
        if e.stderr:
            print(f"تفاصيل الخطأ: {e.stderr}")
        return False


def create_distribution():
    """إنشاء حزمة التوزيع النهائية"""
    print("\n📦 إنشاء حزمة التوزيع النهائية...")
    
    dist_folder = "نظام_المحاسبة_العصري_v2_نهائي_main"
    
    # إنشاء مجلد التوزيع
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    os.makedirs(dist_folder)
    
    # نسخ الملفات المطلوبة
    files_to_copy = [
        ("dist_final/نظام_المحاسبة_العصري_v2_نهائي.exe", "نظام_المحاسبة_العصري_v2.exe"),
        ("README.md", "دليل_المستخدم.txt"),
        ("version_info.json", "معلومات_الإصدار.json"),
        ("main.py", "main_source.py")  # نسخ main.py كمرجع
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, os.path.join(dist_folder, dst))
            print(f"✅ نسخ {dst}")
        else:
            print(f"⚠️ {src} غير موجود")
    
    # إنشاء ملف تشغيل محسن
    batch_content = '''@echo off
chcp 65001 > nul
title نظام المحاسبة العصري v2.0 - النسخة النهائية
echo.
echo ========================================
echo    🏢 نظام المحاسبة العصري v2.0
echo    📁 مبني من main.py الأصلي
echo ========================================
echo.
echo 🚀 جاري تشغيل البرنامج...
echo.

if exist "نظام_المحاسبة_العصري_v2.exe" (
    start "" "نظام_المحاسبة_العصري_v2.exe"
    echo ✅ تم تشغيل البرنامج بنجاح
    echo 💡 البرنامج مبني من main.py مع جميع التصميمات الأصلية
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 💡 تأكد من وجود الملف في نفس المجلد
    pause
)

timeout /t 3 > nul
'''
    
    with open(os.path.join(dist_folder, "تشغيل_البرنامج_النهائي.bat"), "w", encoding="utf-8") as f:
        f.write(batch_content)
    
    print("✅ تم إنشاء ملف التشغيل المحسن")
    
    # نسخ مجلد الأصول إذا كان موجوداً
    if os.path.exists("assets"):
        shutil.copytree("assets", os.path.join(dist_folder, "assets"))
        print("✅ نسخ مجلد الأصول")
    
    print(f"✅ تم إنشاء حزمة التوزيع النهائية في: {dist_folder}")
    return dist_folder


def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("\n🧹 تنظيف الملفات المؤقتة...")
    
    temp_folders = ["build_temp", "__pycache__", "dist_final"]
    temp_files = ["*.spec"]
    
    for folder in temp_folders:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"🗑️ حذف {folder}")
    
    print("✅ تم التنظيف")


def main():
    """الدالة الرئيسية للبناء النهائي"""
    print_header()
    
    # التحقق من ملف main.py
    if not verify_main_py():
        print("\n❌ مشكلة في ملف main.py")
        return False
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        return False
    
    # إنشاء الملفات المطلوبة
    create_version_info()
    create_readme()
    
    # بناء الملف التنفيذي
    if not build_executable():
        print("\n❌ فشل في بناء الملف التنفيذي")
        return False
    
    # إنشاء حزمة التوزيع
    dist_folder = create_distribution()
    
    # تنظيف الملفات المؤقتة
    cleanup()
    
    # النتيجة النهائية
    print("\n" + "=" * 80)
    print("🎉 تم بناء البرنامج بنجاح من main.py!")
    print("=" * 80)
    print(f"📁 مجلد التوزيع: {dist_folder}")
    print("📋 الملفات المتضمنة:")
    print("   • نظام_المحاسبة_العصري_v2.exe - الملف التنفيذي")
    print("   • تشغيل_البرنامج_النهائي.bat - ملف التشغيل")
    print("   • دليل_المستخدم.txt - دليل الاستخدام")
    print("   • معلومات_الإصدار.json - تفاصيل الإصدار")
    print("   • main_source.py - نسخة من main.py المستخدم")
    print("\n🚀 البرنامج جاهز للتوزيع والاختبار!")
    print("💡 تم البناء باستخدام main.py الأصلي مع جميع التصميمات والتقسيمات")
    print("=" * 80)
    
    return True


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ تم البناء بنجاح من main.py!")
    else:
        print("\n❌ فشل في البناء!")
    
    input("\nاضغط Enter للخروج...")
