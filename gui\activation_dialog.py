from PyQt5.QtWidgets import (Q<PERSON><PERSON>og, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox, QApplication)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon
import pyperclip
from license_manager import LicenseManager
from main import resource_path
import os

class ActivationDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.license_manager = LicenseManager()
        self.setWindowTitle("تفعيل البرنامج - معلومات هامة")
        self.setWindowIcon(QIcon(resource_path(os.path.join('assets', 'icons.ico'))))
        self.setMinimumWidth(550)
        self.setLayoutDirection(Qt.RightToLeft)
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # Title
        title_label = QLabel("مرحباً بك في نظام المحاسبة العصري")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # Instructions
        instructions_label = QLabel(
            "لتفعيل نسختك أو الحصول على الدعم، يرجى تزويدنا بالبيانات التالية:"
        )
        instructions_label.setFont(QFont("Arial", 11))
        instructions_label.setAlignment(Qt.AlignCenter)
        instructions_label.setWordWrap(True)
        main_layout.addWidget(instructions_label)

        # --- Customer and Machine Info ---
        info_layout = QVBoxLayout()
        info_layout.setSpacing(10)

        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()

        self.customer_code_edit = self.create_info_field("🔑 كود العميل:", customer_code)
        self.machine_id_edit = self.create_info_field("💻 رقم الجهاز:", machine_id)

        info_layout.addWidget(self.customer_code_edit['label'])
        info_layout.addWidget(self.customer_code_edit['input'])
        info_layout.addWidget(self.machine_id_edit['label'])
        info_layout.addWidget(self.machine_id_edit['input'])
        
        main_layout.addLayout(info_layout)

        # --- Buttons ---
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.copy_button = QPushButton("📋 نسخ المعلومات")
        self.copy_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.copy_button.setStyleSheet("background-color: #3498db; color: white; padding: 10px;")
        self.copy_button.clicked.connect(self.copy_info)

        self.continue_button = QPushButton("متابعة (بدء الفترة التجريبية)")
        self.continue_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.continue_button.setStyleSheet("background-color: #27ae60; color: white; padding: 10px;")
        self.continue_button.clicked.connect(self.accept)

        buttons_layout.addWidget(self.copy_button)
        buttons_layout.addWidget(self.continue_button)
        main_layout.addLayout(buttons_layout)

    def create_info_field(self, label_text, value_text):
        label = QLabel(label_text)
        label.setFont(QFont("Arial", 12, QFont.Bold))
        
        line_edit = QLineEdit(value_text)
        line_edit.setFont(QFont("Arial", 12))
        line_edit.setReadOnly(True)
        line_edit.setAlignment(Qt.AlignCenter)
        line_edit.setStyleSheet("padding: 5px; border: 1px solid #ccc; border-radius: 5px;")

        return {"label": label, "input": line_edit}

    def copy_info(self):
        customer_code = self.customer_code_edit['input'].text()
        machine_id = self.machine_id_edit['input'].text()
        info_to_copy = f"كود العميل: {customer_code}\nرقم الجهاز: {machine_id}"
        
        try:
            pyperclip.copy(info_to_copy)
            QMessageBox.information(self, "تم النسخ", "تم نسخ معلومات التفعيل إلى الحافظة بنجاح.")
        except Exception as e:
            QMessageBox.warning(self, "خطأ في النسخ", f"لم نتمكن من النسخ التلقائي. يرجى نسخ البيانات يدوياً.\n\n{info_to_copy}")
            print(f"Clipboard error: {e}")

if __name__ == '__main__':
    import sys
    app = QApplication(sys.argv)
    dialog = ActivationDialog()
    sys.exit(dialog.exec_())