#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إعدادات الشركة في الواجهة الرئيسية وفواتير الطباعة
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def test_company_settings_file():
    """اختبار وجود وقراءة ملف إعدادات الشركة"""
    print("🔍 اختبار ملف إعدادات الشركة...")
    
    settings_file = "company_settings.json"
    
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            print("✅ تم العثور على ملف الإعدادات")
            print(f"📋 اسم الشركة: {settings.get('company_name', 'غير محدد')}")
            print(f"📧 البريد الإلكتروني: {settings.get('email', 'غير محدد')}")
            print(f"📞 الهاتف: {settings.get('phone', 'غير محدد')}")
            print(f"🖼️ مسار الشعار: {settings.get('logo_path', 'غير محدد')}")
            
            return True, settings
            
        except Exception as e:
            print(f"❌ خطأ في قراءة ملف الإعدادات: {e}")
            return False, {}
    else:
        print("⚠️ ملف الإعدادات غير موجود")
        return False, {}

def test_company_settings_utils():
    """اختبار دوال إعدادات الشركة في utils"""
    print("\n🔍 اختبار دوال إعدادات الشركة...")
    
    try:
        from utils.company_settings import get_company_settings, CompanySettings
        
        # اختبار تحميل الإعدادات
        settings = get_company_settings()
        print("✅ تم تحميل الإعدادات بنجاح")
        print(f"📋 اسم الشركة: {settings.get('company_name', 'غير محدد')}")
        
        # اختبار كلاس CompanySettings
        company_settings = CompanySettings()
        loaded_settings = company_settings.load_settings()
        print("✅ تم تحميل الإعدادات عبر الكلاس بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال الإعدادات: {e}")
        return False

def test_main_window_settings():
    """اختبار تحميل إعدادات الشركة في الواجهة الرئيسية"""
    print("\n🔍 اختبار إعدادات الشركة في الواجهة الرئيسية...")
    
    try:
        from sqlalchemy import create_engine
        from database.users import init_db
        
        # إنشاء قاعدة بيانات مؤقتة للاختبار
        engine = create_engine('sqlite:///test_accounting.db', echo=False)
        init_db(engine)
        
        from gui.main_window import MainWindow
        
        app = QApplication(sys.argv)
        
        # إنشاء مستخدم وهمي للاختبار
        class MockUser:
            def __init__(self):
                self.username = "test_user"
                self.full_name = "مستخدم تجريبي"
                self.roles = []
        
        user = MockUser()
        window = MainWindow(engine=engine, user=user)
        
        # اختبار تحميل إعدادات الشركة
        if hasattr(window, 'company_settings'):
            print("✅ تم تحميل إعدادات الشركة في الواجهة الرئيسية")
            print(f"📋 اسم الشركة: {window.company_settings.get('company_name', 'غير محدد')}")
            
            # اختبار وجود دالة تحديث الإعدادات
            if hasattr(window, 'load_company_settings'):
                print("✅ دالة تحميل الإعدادات موجودة")
            
            if hasattr(window, 'refresh_company_info'):
                print("✅ دالة تحديث معلومات الشركة موجودة")
            
            if hasattr(window, 'load_company_logo'):
                print("✅ دالة تحميل شعار الشركة موجودة")
        else:
            print("❌ لم يتم تحميل إعدادات الشركة")
            return False
        
        # إغلاق النافذة
        window.close()
        app.quit()
        
        # حذف قاعدة البيانات المؤقتة
        if os.path.exists('test_accounting.db'):
            os.remove('test_accounting.db')
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة الرئيسية: {e}")
        return False

def test_invoice_printer_settings():
    """اختبار إعدادات الشركة في نظام الطباعة"""
    print("\n🔍 اختبار إعدادات الشركة في نظام الطباعة...")
    
    try:
        from utils.new_design_invoice_printer import NewDesignInvoicePrinter
        from utils.company_settings import get_company_settings
        
        # اختبار تحميل الإعدادات في نظام الطباعة
        settings = get_company_settings()
        print("✅ تم تحميل الإعدادات في نظام الطباعة")
        print(f"📋 اسم الشركة: {settings.get('company_name', 'غير محدد')}")
        
        # اختبار أن نظام الطباعة يستخدم الإعدادات
        # (لا نحتاج لإنشاء نافذة طباعة فعلية)
        print("✅ نظام الطباعة يدعم إعدادات الشركة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الطباعة: {e}")
        return False

def test_settings_update():
    """اختبار تحديث الإعدادات"""
    print("\n🔍 اختبار تحديث الإعدادات...")
    
    try:
        from utils.company_settings import CompanySettings
        
        # إنشاء إعدادات تجريبية
        company_settings = CompanySettings()
        test_settings = {
            "company_name": "شركة الاختبار",
            "phone": "0123456789",
            "email": "<EMAIL>",
            "address": "عنوان تجريبي"
        }
        
        # حفظ الإعدادات التجريبية
        if company_settings.save_settings(test_settings):
            print("✅ تم حفظ الإعدادات التجريبية")
            
            # إعادة تحميل الإعدادات للتأكد
            loaded_settings = company_settings.load_settings()
            if loaded_settings.get('company_name') == 'شركة الاختبار':
                print("✅ تم تحديث الإعدادات بنجاح")
                return True
            else:
                print("❌ فشل في تحديث الإعدادات")
                return False
        else:
            print("❌ فشل في حفظ الإعدادات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تحديث الإعدادات: {e}")
        return False

def main():
    """تشغيل جميع اختبارات إعدادات الشركة"""
    print("🧪 بدء اختبار إعدادات الشركة...")
    print("=" * 60)
    
    tests = [
        ("ملف إعدادات الشركة", test_company_settings_file),
        ("دوال إعدادات الشركة", test_company_settings_utils),
        ("الواجهة الرئيسية", test_main_window_settings),
        ("نظام الطباعة", test_invoice_printer_settings),
        ("تحديث الإعدادات", test_settings_update)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبارات إعدادات الشركة:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 النتيجة النهائية: {passed}/{len(results)} اختبارات نجحت")
    
    if passed == len(results):
        print("🎉 جميع اختبارات إعدادات الشركة نجحت!")
        print("✅ إعدادات الشركة تعمل بشكل صحيح في:")
        print("   - الواجهة الرئيسية")
        print("   - فواتير الطباعة")
        print("   - التحديث التلقائي")
    else:
        print("⚠️ بعض اختبارات إعدادات الشركة تحتاج مراجعة")

if __name__ == "__main__":
    main()
