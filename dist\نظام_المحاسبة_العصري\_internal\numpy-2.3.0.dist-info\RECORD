../../Scripts/f2py.exe,sha256=g7gSBf5Aw94HkWOIz5liYk42P74agDiuHf_G6Dc26vM,108421
../../Scripts/numpy-config.exe,sha256=x7IV2LnWqDrLrAQJOIPkVLqPB7A1hFWvxj5-CkvURIo,108421
numpy-2.3.0.dist-info/DELVEWHEEL,sha256=rbi5sxD2V9JavasSdRM3Jb0vLdkepPtWX2jC0-qatC4,446
numpy-2.3.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
numpy-2.3.0.dist-info/LICENSE.txt,sha256=Iz16tcMrxK7WhI9kfTlwB5ZazbaRyG_ifRfMPrYDWdE,47722
numpy-2.3.0.dist-info/METADATA,sha256=gySfCMncez4ckcfeIPQwmH8pWeIMUooVX3gwS-GE7Fo,60884
numpy-2.3.0.dist-info/RECORD,,
numpy-2.3.0.dist-info/WHEEL,sha256=suq8ARrxbiI7iLH3BgK-82uzxQ-4Hm-m8w01oCokrtA,85
numpy-2.3.0.dist-info/entry_points.txt,sha256=7Cb63gyL2sIRpsHdADpl6xaIW5JTlUI-k_yqEVr0BSw,220
numpy.libs/libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll,sha256=ZUfp-5Zul3PK7idV6RqL9NbzovDuv5ZGsBWPhnXqSrU,20390912
numpy.libs/msvcp140-263139962577ecda4cd9469ca360a746.dll,sha256=pMIim9wqKmMKzcCVtNhgCOXD47x3cxdDVPPaT1vrnN4,575056
numpy/__config__.py,sha256=i6urB4TH3UC2lpi6y8EvBbyY-x3TlunbIV46uhg9NeE,5693
numpy/__config__.pyi,sha256=Zmg85dEvXfCQUlQ2VoZq2OPTV2MuwcyhHQW4RDkvJg8,2469
numpy/__init__.cython-30.pxd,sha256=_dQ25sYPbiOsW3ACUNSDN1bgU6NCEJiY5UcTKBhbCu0,48364
numpy/__init__.pxd,sha256=7jgfd5hTwIsb4KuAiAguiEUdLjsPm2gEr7eDNgq3itM,44912
numpy/__init__.py,sha256=_i_sB4izOj-YQaEKzUfDk2E41y81tHszQ6INx2kgm60,26476
numpy/__init__.pyi,sha256=eDfTDyM9Rh8kxUJ3T9kK96bnY9nyzoORn8bUHTmmPYo,218188
numpy/__pycache__/__config__.cpython-313.pyc,,
numpy/__pycache__/__init__.cpython-313.pyc,,
numpy/__pycache__/_array_api_info.cpython-313.pyc,,
numpy/__pycache__/_configtool.cpython-313.pyc,,
numpy/__pycache__/_distributor_init.cpython-313.pyc,,
numpy/__pycache__/_expired_attrs_2_0.cpython-313.pyc,,
numpy/__pycache__/_globals.cpython-313.pyc,,
numpy/__pycache__/_pytesttester.cpython-313.pyc,,
numpy/__pycache__/conftest.cpython-313.pyc,,
numpy/__pycache__/dtypes.cpython-313.pyc,,
numpy/__pycache__/exceptions.cpython-313.pyc,,
numpy/__pycache__/matlib.cpython-313.pyc,,
numpy/__pycache__/version.cpython-313.pyc,,
numpy/_array_api_info.py,sha256=kwJnMXhnzMExiWj5e6SLBGkPfOVoxMXyZLizSwSZNZc,10727
numpy/_array_api_info.pyi,sha256=xDNFMjgROER2wnf2C8zchoxLByR_WKEayxWCy5RQi3w,5071
numpy/_configtool.py,sha256=qqay_oP0bqkryU55jFBE6RzgKJkquJ0bBvaYt9D-Gbs,1046
numpy/_configtool.pyi,sha256=IlC395h8TlcZ4DiSW5i6NBQO9I74ERfXpwSYAktzoaU,25
numpy/_core/__init__.py,sha256=Mn9BOga5ck1QA_T6MO3h5G16jzYv7TZ8fL2KFV6k4_g,5728
numpy/_core/__init__.pyi,sha256=C5NQDIktXlR1OosGgyvY87pyotkyJr3Ci2dMWTLpSi4,88
numpy/_core/__pycache__/__init__.cpython-313.pyc,,
numpy/_core/__pycache__/_add_newdocs.cpython-313.pyc,,
numpy/_core/__pycache__/_add_newdocs_scalars.cpython-313.pyc,,
numpy/_core/__pycache__/_asarray.cpython-313.pyc,,
numpy/_core/__pycache__/_dtype.cpython-313.pyc,,
numpy/_core/__pycache__/_dtype_ctypes.cpython-313.pyc,,
numpy/_core/__pycache__/_exceptions.cpython-313.pyc,,
numpy/_core/__pycache__/_internal.cpython-313.pyc,,
numpy/_core/__pycache__/_machar.cpython-313.pyc,,
numpy/_core/__pycache__/_methods.cpython-313.pyc,,
numpy/_core/__pycache__/_string_helpers.cpython-313.pyc,,
numpy/_core/__pycache__/_type_aliases.cpython-313.pyc,,
numpy/_core/__pycache__/_ufunc_config.cpython-313.pyc,,
numpy/_core/__pycache__/arrayprint.cpython-313.pyc,,
numpy/_core/__pycache__/cversions.cpython-313.pyc,,
numpy/_core/__pycache__/defchararray.cpython-313.pyc,,
numpy/_core/__pycache__/einsumfunc.cpython-313.pyc,,
numpy/_core/__pycache__/fromnumeric.cpython-313.pyc,,
numpy/_core/__pycache__/function_base.cpython-313.pyc,,
numpy/_core/__pycache__/getlimits.cpython-313.pyc,,
numpy/_core/__pycache__/memmap.cpython-313.pyc,,
numpy/_core/__pycache__/multiarray.cpython-313.pyc,,
numpy/_core/__pycache__/numeric.cpython-313.pyc,,
numpy/_core/__pycache__/numerictypes.cpython-313.pyc,,
numpy/_core/__pycache__/overrides.cpython-313.pyc,,
numpy/_core/__pycache__/printoptions.cpython-313.pyc,,
numpy/_core/__pycache__/records.cpython-313.pyc,,
numpy/_core/__pycache__/shape_base.cpython-313.pyc,,
numpy/_core/__pycache__/strings.cpython-313.pyc,,
numpy/_core/__pycache__/umath.cpython-313.pyc,,
numpy/_core/_add_newdocs.py,sha256=1NH5aPMoQ6UNuEkUDinZ3V3c1CTYTFJzWJuKjF_QL1I,215860
numpy/_core/_add_newdocs.pyi,sha256=ttPc9PlJ6lBkZrBrjzzWD4_jxmkIxpojL8RWR-d3e1c,171
numpy/_core/_add_newdocs_scalars.py,sha256=_182PHQWp5ydrXdshKaY_E5cCTln-cWlvETrH1hVKmA,12990
numpy/_core/_add_newdocs_scalars.pyi,sha256=qgD9RUeJdv6bkYewvQPXXCzO_roSKbaueq9PyvS6wSA,589
numpy/_core/_asarray.py,sha256=2n1La1qfXzY_PzNuGNYUpd_DNSRpxvMuza4A9JG_72E,4045
numpy/_core/_asarray.pyi,sha256=_MDCETj47WvhoGOxTchfAg3W7MXiHZD17RKVAxD17XM,1114
numpy/_core/_dtype.py,sha256=TEjPZXHmpw5-HhfGfKS-PfQT9A9sk8cnaE6icAXVb34,10913
numpy/_core/_dtype.pyi,sha256=N9GjR4mqY32i4w30rhQUO6jn_ZwnTPXwt6qSWspCfaw,1909
numpy/_core/_dtype_ctypes.py,sha256=e8EgfaqXiJ8-UYi8FM5sm9W8ehqvcG_rTpDROaKwTKg,3846
numpy/_core/_dtype_ctypes.pyi,sha256=d5BudSdtj6n046OX9c-rUoX5zVGghdoO22yEhkjVRoM,3765
numpy/_core/_exceptions.py,sha256=umxWh9TLhDXy9LmW77wkIHMegFk_KFfBB5ndxsJd5F4,5321
numpy/_core/_exceptions.pyi,sha256=vtB-3qZeZrxsi2nhDQEw4Tr-wx0nidxQhQly07y-97I,1955
numpy/_core/_internal.py,sha256=Mr67dGTf0JB3RPepCBh7aHDUuLeUqtHhCgfCDMvAB9g,29939
numpy/_core/_internal.pyi,sha256=VzozE1T6MXeVNrx5UfIbEhtGRqc07xa_2e3RRIAuIXI,2726
numpy/_core/_machar.py,sha256=hFuVAKRK5tlym0NhT-PpH_gUfbh50ndINbYnK8V0PSs,11924
numpy/_core/_machar.pyi,sha256=vtB-3qZeZrxsi2nhDQEw4Tr-wx0nidxQhQly07y-97I,1955
numpy/_core/_methods.py,sha256=iuo5uW5_zRJCessGY08qjWZLwQo88aUvEr6XdIueTKM,9685
numpy/_core/_methods.pyi,sha256=7Mc4H9O3KYU9VwCsOo8X0q6mg9vDr2S6xbwuJ7PXPX4,548
numpy/_core/_multiarray_tests.cp313-win_amd64.lib,sha256=5Jwnm2NhRdXL8MCNJz4Uu37z091cLA5gUr5m1f5tI4g,2418
numpy/_core/_multiarray_tests.cp313-win_amd64.pyd,sha256=2LuNto8UcvppTscscYTtJd0G0pAly5Q7fWoiCD-Ymso,63488
numpy/_core/_multiarray_umath.cp313-win_amd64.lib,sha256=YMqYpmyzeWoN-0ypkisDrLoS3O-_MUL7aRotOvO0IFw,2192
numpy/_core/_multiarray_umath.cp313-win_amd64.pyd,sha256=qPvOv9wLR2apvaFjpvVqEU4YxpvqIEPx_S1IwjxflS0,4472320
numpy/_core/_operand_flag_tests.cp313-win_amd64.lib,sha256=sBjf-cskpBpQ76FX5VWy1NE2jotJREP1DEYfm-Zztmg,2228
numpy/_core/_operand_flag_tests.cp313-win_amd64.pyd,sha256=Q_qHtSsFt5SEwvbPR3z9n6Df73iE0CvPuvJfpjNWSWE,12288
numpy/_core/_rational_tests.cp313-win_amd64.lib,sha256=UFOJWZUeQ2q0NZwdrZ8O29ZQJZpTYUMxFFTQl-dQPGE,2156
numpy/_core/_rational_tests.cp313-win_amd64.pyd,sha256=pFP1CMmhC-VZNXsJ9gt32UJlvrOnjsQQuHXiesbIr8w,39936
numpy/_core/_simd.cp313-win_amd64.lib,sha256=Do_k37V0-yLUpdCBluInalfWFrtP8eLTNgAJNz_p04k,1976
numpy/_core/_simd.cp313-win_amd64.pyd,sha256=q4zG17uk8fcYuuEuZqdd5Qe2v65EpeVRb5q_CYiFATg,2411008
numpy/_core/_simd.pyi,sha256=RN-uZiTi3jZNOgOOKlu97Mu1Ufkb8jvLUDDEnaW77Lc,694
numpy/_core/_string_helpers.py,sha256=aX1N5UsNeUFy54o5tuTC6X6N9AJueFN2_6QIyIUD2Xg,2945
numpy/_core/_string_helpers.pyi,sha256=bThH7ichGlrmQ6O38n71QcJ7Oi_mRPUf-DFMU5hdYU0,370
numpy/_core/_struct_ufunc_tests.cp313-win_amd64.lib,sha256=iD-VoH4aslpJECeGHG9mzIQO6ExGvapoGyYXBAvH5tM,2228
numpy/_core/_struct_ufunc_tests.cp313-win_amd64.pyd,sha256=qrDFECaOHVw1JE0k67EjtZlEIsFSdP4E1HTfflFhwPg,14336
numpy/_core/_type_aliases.py,sha256=qNQdR-XjGM4sUAiVGMklurGJQXyYjSe2ytqyjQYNWf8,3608
numpy/_core/_type_aliases.pyi,sha256=gRut9t3JHo4DQwf454S0Z-yGAxlW-OKzpXM0Aj7RRqg,2485
numpy/_core/_ufunc_config.py,sha256=9fvB4g8cJY1KhR1REqsrf2hJ9w7el43BPaEbH5gp-wg,15541
numpy/_core/_ufunc_config.pyi,sha256=gI899DauI0R8RIBMvlpH8j3PDKGSS30oN_tx8FnpQCg,1004
numpy/_core/_umath_tests.cp313-win_amd64.lib,sha256=XmjzvarlTFgu1ZG3HAGDS1Xr2KKCKVuuEW_qDL_EMdk,2104
numpy/_core/_umath_tests.cp313-win_amd64.pyd,sha256=87uY-Wk-2JwaYzb8sJ6I-KeEyzivswRXsAcWShpmUHw,34304
numpy/_core/arrayprint.py,sha256=27hUDD-20L_P9lp84a-LJu1mfEvNX0zPb9qjkfKRcfs,67053
numpy/_core/arrayprint.pyi,sha256=3ac2DS9uMuUZ86hnmpM0vltS1BGLuNsXHXLHwNH6c5I,7209
numpy/_core/cversions.py,sha256=FISv1d4R917Bi5xJjKKy8Lo6AlFkV00WvSoB7l3acA4,360
numpy/_core/defchararray.py,sha256=XHaUdJm_pWX8MCWM9CZCyAMw5Wi38qxJ6AnBMgUYgf4,39434
numpy/_core/defchararray.pyi,sha256=bxHHZ3T_5VSbaaKtvr8KNZ05KRRcHR1VH3zfkD5wrjs,27957
numpy/_core/einsumfunc.py,sha256=8agj-ZqYEkfxoADYH1DEC-BLvMUSdQyKssVvAkFCzFg,54318
numpy/_core/einsumfunc.pyi,sha256=h4nfk-sGpvTVmvP04BOl_PGcfNZxh2-E11bWdihfepg,5077
numpy/_core/fromnumeric.py,sha256=TQO9AWCHVh5zKWV7NfvpYd8T3-0e2med1XX0dSlbNMw,148107
numpy/_core/fromnumeric.pyi,sha256=YwpkWSsQc9JY8GpQVvDYfGMZ0AHMTIb4HYIvcLbk4F4,43671
numpy/_core/function_base.py,sha256=D2JmNn3VOCCxdhnno_gK6ZeuQOdS2VoV1xLIJy-eLUQ,20228
numpy/_core/function_base.pyi,sha256=hp3h1N0WHvdIHtTtLLqaUhjrDiLW8Ud1Wzu8rZC5Bi4,7342
numpy/_core/getlimits.py,sha256=N5j3lVeeA0yfpuDk93fKkfSs9qCuJ1xsXoDMSmzZMaI,26849
numpy/_core/getlimits.pyi,sha256=3u55btDSVkpbsnFxkCWqRY7LZ1WhGop_LAUnjJfOUR8,64
numpy/_core/include/numpy/__multiarray_api.c,sha256=ucLypGZeaaHhl2OX-4YQrrCGTUl-8XwObmmN8zQjRjU,13074
numpy/_core/include/numpy/__multiarray_api.h,sha256=k8TnD4kVS-yNbe5s9yuZXifoJxXU1J04ElZt7ut3jmA,63261
numpy/_core/include/numpy/__ufunc_api.c,sha256=NoTcyLqrAF8F3AE0TDvlDFS7DXuFJRpoINEaDnZWhys,1809
numpy/_core/include/numpy/__ufunc_api.h,sha256=M-szFmRhn9IsEmaLO_fjq0VB58IvgVJTq4G3oB1bvas,13516
numpy/_core/include/numpy/_neighborhood_iterator_imp.h,sha256=s5TK2aPpClbw4CbVJCij__hzoh5IgHIIZK0k6FKtqfc,1947
numpy/_core/include/numpy/_numpyconfig.h,sha256=7vfr5P9qM9rTNTCrkirW3Q0pm4TD83_7yjl8nY3r6bY,902
numpy/_core/include/numpy/_public_dtype_api_table.h,sha256=4ylG8s52kZEx__QODt_7Do8QitmhDSvTeZ7Lar0fOgo,4660
numpy/_core/include/numpy/arrayobject.h,sha256=ghWzloPUkSaVkcsAnBnpbrxtXeXL-mkzVGJQEHFxjnk,211
numpy/_core/include/numpy/arrayscalars.h,sha256=4TrsilxaUiH4mVCkElEPTM_C_8c67O9R4Whx-3QzDE4,4439
numpy/_core/include/numpy/dtype_api.h,sha256=cfQuPb0zrVqYFdWauOqbgdXR8rtm4DjNz2nbfSWvSRo,19718
numpy/_core/include/numpy/halffloat.h,sha256=qYgX5iQfNzXICsnd0MCRq5ELhhfFjlRGm1xXGimQm44,2029
numpy/_core/include/numpy/ndarrayobject.h,sha256=V5Zkf5a9vWyV8ZInBgAceBn7c9GK4aquhzeGTW_Sgls,12361
numpy/_core/include/numpy/ndarraytypes.h,sha256=-hJA5FBQv2WcWRYSfRcYMAAsH54KnsIDudcOai4nn1w,67760
numpy/_core/include/numpy/npy_2_compat.h,sha256=VxsRXAtDfLlXkvH-ErZRSuH49k9EjcFwcSUSfTPRzAU,8795
numpy/_core/include/numpy/npy_2_complexcompat.h,sha256=uW0iF-qMwQNn4PvIfWCrYce6b4OrYUO4BWu-VYYAZag,885
numpy/_core/include/numpy/npy_3kcompat.h,sha256=dV01ltbxntPY8cN7WAL4MX3KHeyCLeSBDQreDxs09aQ,10022
numpy/_core/include/numpy/npy_common.h,sha256=1Dt0W8dTFbjB3Ra3MpNlA0qcWNPZD5R_ztb3uCNE5uI,33563
numpy/_core/include/numpy/npy_cpu.h,sha256=bTcimfKHGMeNLH-wYfXf7foYM6RenkTiWx4a_D-GYSY,4349
numpy/_core/include/numpy/npy_endian.h,sha256=NZSi-5CbqZ92AUztILDJLBKP61-VQezmAatYTNLwRu8,2912
numpy/_core/include/numpy/npy_math.h,sha256=ksdiKBXDfpEHB1s9m5yinyhjdcc0h-zJcfXEuoVHAd8,19460
numpy/_core/include/numpy/npy_no_deprecated_api.h,sha256=jIcjEP2AbovDTfgE-qtvdP51_dVGjVnEGBX86rlGSKE,698
numpy/_core/include/numpy/npy_os.h,sha256=j044vd1C1oCcW52r3htiVNhUaJSEqCjKrODwMHq3TU0,1298
numpy/_core/include/numpy/numpyconfig.h,sha256=3awsuwxRkv0NxqJmUdLc-g7PoMIW520hqLJFPl4THy4,7515
numpy/_core/include/numpy/random/LICENSE.txt,sha256=1UR2FVi1EIZsIffootVxb8p24LmBF-O2uGMU23JE0VA,1039
numpy/_core/include/numpy/random/bitgen.h,sha256=_H0uXqmnub4PxnJWdMWaNqfpyFDu2KB0skf2wc5vjUc,508
numpy/_core/include/numpy/random/distributions.h,sha256=GLURa3sFESZE0_0RK-3Gqmfa96itBHw8LlsNyy9EPt4,10070
numpy/_core/include/numpy/random/libdivide.h,sha256=F9PLx6TcOk-sd0dObe0nWLyz4HhbHv2K7voR_kolpGU,82217
numpy/_core/include/numpy/ufuncobject.h,sha256=uI5m_WOrFQtaL3BwgRmiZ7BN8CypKXfC5EcQfdhH-Eg,12123
numpy/_core/include/numpy/utils.h,sha256=vzJAbatJYfxHmX2yL_xBirmB4mEGLOhJ92JlV9s8yPs,1222
numpy/_core/lib/npy-pkg-config/mlib.ini,sha256=hYWFyoBxE036dh19si8UPka01H2cv64qlc4ZtgoA_7A,156
numpy/_core/lib/npy-pkg-config/npymath.ini,sha256=e0rdsb00Y93VuammuvIIFlzZtnUAXwsS1XNKlCU8mFQ,381
numpy/_core/lib/npymath.lib,sha256=0-rqIa0E7gatC8_UchpUzzPjavhhg1eAIXhB0yx3drw,157178
numpy/_core/lib/pkgconfig/numpy.pc,sha256=c9gawCnj5gadx2CRgQYaBHA1scu7iQO6KH8QmTVm0z4,198
numpy/_core/memmap.py,sha256=7HWQGjK5bS3SzAPx4wAlHwH6YFrX13sBpa6E52zxatc,13014
numpy/_core/memmap.pyi,sha256=n0kBe4iQD5lcWvAvVhdUU18YIoPX6Sf5e2qh9IdO5uQ,50
numpy/_core/multiarray.py,sha256=ME5DKfJ4RbJ6whslkBbOJIVzdLHbKtBImHpAi_Bx2pY,59917
numpy/_core/multiarray.pyi,sha256=Qcd0pq3kSEkix5Q3ANyHwsHd7g2BC2RsonZXQ_yrmcc,33503
numpy/_core/numeric.py,sha256=-JvXLgXpQIxwM6VGz3btBDnoeRinx67JQ9i1vM7KoNI,85082
numpy/_core/numeric.pyi,sha256=HPxLAdXviPTw-UjrMYqI3Jb2Q-DlF2PeRBGXsaOgDuI,19924
numpy/_core/numerictypes.py,sha256=fBj9GXiGqkOO-5FY8idx-miFHwTExrPDib1dTOlih-s,16590
numpy/_core/numerictypes.pyi,sha256=kqugJm07IibdlojXtRa5dm1bYI367x2DK0IvRBXT9Io,3462
numpy/_core/overrides.py,sha256=Fn7Qi97VW3-tqWJZd9Xgn6eCntB0UkVGmE7g3ToxOW0,7424
numpy/_core/overrides.pyi,sha256=gUulOylgoJ7r92GBLP_qn91qvSQMoTGV9RebZ-waAFU,1761
numpy/_core/printoptions.py,sha256=ZXekBr6fI18dVxsM6bxAGi80CiMlaMN4dpbPHDQiBOI,1088
numpy/_core/printoptions.pyi,sha256=QE36MVL3BgqflyQuj6UOzywbnELMiLeyNz_1sALvOSU,622
numpy/_core/records.py,sha256=F7Cvw2ZUu9h8nGgPtwuDRgTXWsLyP8mnSlczxs5PWSE,37856
numpy/_core/records.pyi,sha256=Tk57V2NJNCKC5nBHQAmdHixhG1asUR1nMx55Yro56SY,9268
numpy/_core/shape_base.py,sha256=ThNuc3aMU94qmCUBh4Zru-QEfK35HW3Kqs5VloGb5Ok,33736
numpy/_core/shape_base.pyi,sha256=nwPDmpBWUXYNT9-5aGOyBzLecyZamcczvKOBj1ioa1g,4928
numpy/_core/strings.py,sha256=IzHeA4hbiyOwaX50axZ_DHV4hU8uQH2JrtOTdF57RRM,52465
numpy/_core/strings.pyi,sha256=yP_unVbtw0jdvS7nvJOqhhmZ8n3JlBThDP3bsuxZeKU,14013
numpy/_core/tests/__pycache__/_locales.cpython-313.pyc,,
numpy/_core/tests/__pycache__/_natype.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test__exceptions.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_abc.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_api.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_argparse.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_array_api_info.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_array_coercion.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_array_interface.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_arraymethod.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_arrayobject.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_arrayprint.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_casting_floatingpoint_errors.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_casting_unittests.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_conversion_utils.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_cpu_dispatcher.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_cpu_features.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_custom_dtypes.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_cython.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_datetime.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_defchararray.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_deprecations.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_dlpack.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_dtype.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_einsum.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_errstate.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_extint128.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_function_base.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_getlimits.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_half.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_hashtable.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_indexerrors.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_indexing.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_item_selection.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_limited_api.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_longdouble.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_machar.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_mem_overlap.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_mem_policy.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_memmap.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_multiarray.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_multithreading.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_nditer.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_nep50_promotions.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_numeric.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_numerictypes.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_overrides.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_print.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_protocols.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_records.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalar_ctors.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalar_methods.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarbuffer.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarinherit.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarmath.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarprint.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_shape_base.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_simd.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_simd_module.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_stringdtype.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_strings.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_ufunc.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_umath.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_umath_accuracy.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_umath_complex.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_unicode.cpython-313.pyc,,
numpy/_core/tests/_locales.py,sha256=byq7PFI0o_eF8Ddsvgj2EQ7oEjgxYZEa2EW0SJmR_xc,2248
numpy/_core/tests/_natype.py,sha256=IB1eKt12ObjMuhK-GLzUbgJpQfy7_Tk08HOPEjaBSC8,6730
numpy/_core/tests/data/astype_copy.pkl,sha256=lWSzCcvzRB_wpuRGj92spGIw-rNPFcd9hwJaRVvfWdk,716
numpy/_core/tests/data/generate_umath_validation_data.cpp,sha256=9TBdxpPo0djv1CKxQ6_DbGKRxIZVawitAm7AMmWKroI,6012
numpy/_core/tests/data/recarray_from_file.fits,sha256=NA0kliz31FlLnYxv3ppzeruONqNYkuEvts5wzXEeIc4,8640
numpy/_core/tests/data/umath-validation-set-README.txt,sha256=GfrkmU_wTjpLkOftWDuGayEDdV3RPpN2GRVQX61VgWI,982
numpy/_core/tests/data/umath-validation-set-arccos.csv,sha256=VUdQdKBFrpXHLlPtX2WYIK_uwkaXgky85CZ4aNuvmD4,62794
numpy/_core/tests/data/umath-validation-set-arccosh.csv,sha256=tbuOQkvnYxSyJf_alGk3Zw3Vyv0HO5dMC1hUle2hWwQ,62794
numpy/_core/tests/data/umath-validation-set-arcsin.csv,sha256=JPEWWMxgPKdNprDq0pH5QhJ2oiVCzuDbK-3WhTKny8o,62768
numpy/_core/tests/data/umath-validation-set-arcsinh.csv,sha256=fwuq25xeS57kBExBuSNfewgHb-mgoR9wUGVqcOXbfoI,61718
numpy/_core/tests/data/umath-validation-set-arctan.csv,sha256=nu33YyL-ALXSSF5cupCTaf_jTPLK_QyUfciNQGpffkY,61734
numpy/_core/tests/data/umath-validation-set-arctanh.csv,sha256=wHSKFY2Yvbv3fnmmfLqPYpjhkEM88YHkFVpZQioyBDw,62768
numpy/_core/tests/data/umath-validation-set-cbrt.csv,sha256=FFi_XxEnGrfJd7OxtjVFT6WFC2tUqKhVV8fmQfb0z8o,62275
numpy/_core/tests/data/umath-validation-set-cos.csv,sha256=ccDri5_jQ84D_kAmSwZ_ztNUPIhzhgycDtNsPB7m8dc,60497
numpy/_core/tests/data/umath-validation-set-cosh.csv,sha256=DnN6RGvKQHAWIofchmhGH7kkJej2VtNwGGMRZGzBkTQ,62298
numpy/_core/tests/data/umath-validation-set-exp.csv,sha256=mPhjF4KLe0bdwx38SJiNipD24ntLI_5aWc8h-V0UMgM,17903
numpy/_core/tests/data/umath-validation-set-exp2.csv,sha256=sD94pK2EAZAyD2fDEocfw1oXNw1qTlW1TBwRlcpbcsI,60053
numpy/_core/tests/data/umath-validation-set-expm1.csv,sha256=tyfZN5D8tlm7APgxCIPyuy774AZHytMOB59H9KewxEs,61728
numpy/_core/tests/data/umath-validation-set-log.csv,sha256=CDPky64PjaURWhqkHxkLElmMiI21v5ugGGyzhdfUbnI,11963
numpy/_core/tests/data/umath-validation-set-log10.csv,sha256=dW6FPEBlRx2pcS-7eui_GtqTpXzOy147il55qdP-8Ak,70551
numpy/_core/tests/data/umath-validation-set-log1p.csv,sha256=2aEsHVcvRym-4535CkvJTsmHywkt01ZMfmjl-d4fvVI,61732
numpy/_core/tests/data/umath-validation-set-log2.csv,sha256=aVZ7VMQ5urGOx5MMMOUmMKBhFLFE-U7y6DVCTeXQfo0,70546
numpy/_core/tests/data/umath-validation-set-sin.csv,sha256=GvPrQUEYMX1iB2zjbfK26JUJOxtqbfiRUgXuAO1QcP0,59981
numpy/_core/tests/data/umath-validation-set-sinh.csv,sha256=lc7OYcYWWpkxbMuRAWmogQ5cKi7EwsQ2ibiMdpJWYbw,61722
numpy/_core/tests/data/umath-validation-set-tan.csv,sha256=fn7Dr9s6rcqGUzsmyJxve_Z18J4AUaSm-uo2N3N_hfk,61728
numpy/_core/tests/data/umath-validation-set-tanh.csv,sha256=xSY5fgfeBXN6fal4XDed-VUcgFIy9qKOosa7vQ5v1-U,61728
numpy/_core/tests/examples/cython/__pycache__/setup.cpython-313.pyc,,
numpy/_core/tests/examples/cython/checks.pyx,sha256=Ayf5e8WueUGNzwKn6VuF4xHREPgwj_uu8eot6Q7n4eU,11147
numpy/_core/tests/examples/cython/meson.build,sha256=EaUdTgpleUBROExDaFVMnWIYW4XDxFLFGK9ej_pTtQg,1311
numpy/_core/tests/examples/cython/setup.py,sha256=h5vJxfwGpwRWaa7iWTYeCstbcDNHN0Yd_rP963v7sZ0,898
numpy/_core/tests/examples/limited_api/__pycache__/setup.cpython-313.pyc,,
numpy/_core/tests/examples/limited_api/limited_api1.c,sha256=RcHe_nyyjv86gjF9E53cexQiGW-YNs8OGGqjrxCFhBc,363
numpy/_core/tests/examples/limited_api/limited_api2.pyx,sha256=4P5-yu0yr8NBa-TFtw4v30LGjccRroRAQFFLaztEK9I,214
numpy/_core/tests/examples/limited_api/limited_api_latest.c,sha256=drvrNSyOeF0Or0trDmayJWllTP7c4Nzpp9T0ydwPAGo,471
numpy/_core/tests/examples/limited_api/meson.build,sha256=yitMzLuGDhWCjyavpm5UEBrhwKnfXOVAxA3ZL7PlB0Q,1686
numpy/_core/tests/examples/limited_api/setup.py,sha256=47iWsN-5wYB29Lb7vqSjzrAS3UtkdFufkt93XzzG-lE,461
numpy/_core/tests/test__exceptions.py,sha256=ov3cdaYBfP28w_FcLF57ROlF5w6fwCFRNcmOVyRA-IU,3012
numpy/_core/tests/test_abc.py,sha256=qdC7_lkQvaF_3A4xJ9H_Ih3FDlMpA9dxQHjsg4Tn-uc,2275
numpy/_core/tests/test_api.py,sha256=t15zVo3WWuFFkjw-ppGDYJZirJ7C9b2f6ixwE6plUNE,23575
numpy/_core/tests/test_argparse.py,sha256=AsDsGRknJq1XejjkuCjx3oSBZZHdy7K9fqkwqV1VSNc,2962
numpy/_core/tests/test_array_api_info.py,sha256=YySxzABrxjo2XVC9bwslv5VGBIiDK5N0DXpKLfhwBio,3176
numpy/_core/tests/test_array_coercion.py,sha256=LAnfdSkKW2VEa-3aEzSX6Mj3ejEeZa63fLbLmSy2OH8,35808
numpy/_core/tests/test_array_interface.py,sha256=s-mrGDOBpWOfIShNHrnfPuUeZDTBX5eD8R1kY4-JrUc,8065
numpy/_core/tests/test_arraymethod.py,sha256=q4MgPQLLJxu4fUtdMSsyECkErowPwraWgYAJp6veXrU,3307
numpy/_core/tests/test_arrayobject.py,sha256=cQu4aDjyF6EgoiGe5UISyOHGx5QEkdGvbfCXVuKjHQ8,2671
numpy/_core/tests/test_arrayprint.py,sha256=Q0rOUlo0kSmrU3LxaBYOTnMzcYfcIUV0gy4khNzdujI,52066
numpy/_core/tests/test_casting_floatingpoint_errors.py,sha256=fMotyIWxYMxJ_mF7zZMg3j3l7j-C6nfm_YUPw1ln5dA,5230
numpy/_core/tests/test_casting_unittests.py,sha256=J4Qt1tVacrgj8brpZNZ0-IOlZQP8h92YI6v5l6GmCS0,35153
numpy/_core/tests/test_conversion_utils.py,sha256=-RMHwc8xPyymb1i4ZxbFlxIqCx7dOwgpuHYukV-IhUc,6568
numpy/_core/tests/test_cpu_dispatcher.py,sha256=xIWptwLDyBPiuAGpLryuK3TgFJIbtYTKSZSFfxLzzgQ,1619
numpy/_core/tests/test_cpu_features.py,sha256=GCjzPHRQUKTPYXP6i00mTHSfHYGIAB5SNBN22iwXj-A,16135
numpy/_core/tests/test_custom_dtypes.py,sha256=krzdkviMesBtOGM51ZC3O16LnInMFBo1ZMAgWReZfpM,12081
numpy/_core/tests/test_cython.py,sha256=DSGqV-hBtvJ0AD_uk44YMe1EZEkYQy7B0pO1YNJc194,10537
numpy/_core/tests/test_datetime.py,sha256=_QlWdLriVm3YSZz9LWBJ_qOppH1GG811ChNGN709Fhg,124496
numpy/_core/tests/test_defchararray.py,sha256=NRSghsMq0UY3MckWTl2sn8FQE7ls8amAHnztrs3PAvo,31454
numpy/_core/tests/test_deprecations.py,sha256=mXihH4FepKCqlUT6Ks8FU77ZkxDfgbhS16dDm6pjhqw,17555
numpy/_core/tests/test_dlpack.py,sha256=_wHOWg22PT6U6Py0BGOwaB7mTrQV219Ycy6EXMbFnZ8,6020
numpy/_core/tests/test_dtype.py,sha256=c7H-FgmDmpaN2CSgx1ZfjKoIGK7M9mhUbjMDxso6w50,80792
numpy/_core/tests/test_einsum.py,sha256=2rcE0fmKLNIWhwPnV0_7DV_Q4UYHxUmyMO8UwPOlHrk,57594
numpy/_core/tests/test_errstate.py,sha256=FB5-P9YNDk07HQVtvND1ZreTKzZyedorlZ8w38DoGrM,4758
numpy/_core/tests/test_extint128.py,sha256=tJMy0G0YhhV8EnRUN9lybMAqUgdBMhfsQZWXB05XKDc,5842
numpy/_core/tests/test_function_base.py,sha256=r13rRmNIJEgc0VsAPnb1yDZxlh7ZI_sR7w4EH9uqzfw,18154
numpy/_core/tests/test_getlimits.py,sha256=F7alzOWMDfBoF1W2cukpyOFQdBBbTgZ_OI9tqLVo4fI,7181
numpy/_core/tests/test_half.py,sha256=7iO3jBtzuIWyp1_H008oGd04myfIptoKdgKll5o5NXE,24993
numpy/_core/tests/test_hashtable.py,sha256=tiixi7rz7lGoDpn4mrzqTWB3jWL6UV1DNK-bQ2393_A,1182
numpy/_core/tests/test_indexerrors.py,sha256=-zO-IvbIaab8GmA7INyBlDMCdBPtdflzLd7ZdFvyUXA,4851
numpy/_core/tests/test_indexing.py,sha256=6dTuv0nTXdjTMkLMe0EPpOaJMm8-dxAy5ou5LkytXqs,56876
numpy/_core/tests/test_item_selection.py,sha256=erSTKqbX9C5i9EJeTI4tIenDW4vlo1PO-wqFRKtBB68,6798
numpy/_core/tests/test_limited_api.py,sha256=bN6sU8V2vtFOxwhf3n0EtEXJuT5ifw2i6S2DHTDJHMQ,3565
numpy/_core/tests/test_longdouble.py,sha256=KeTNI4tY-h7Tklmo--j0MD0pTqaE7voknrl2SfupDRY,12760
numpy/_core/tests/test_machar.py,sha256=68mp4juIpLILsQCRO3wBNUqMaZuNqtss_8TUTeQkD_g,1097
numpy/_core/tests/test_mem_overlap.py,sha256=Zoy0yf1jGCugt9dxWK5Drb413vP_gTwzW14SlFsAik8,30149
numpy/_core/tests/test_mem_policy.py,sha256=P2hNYfmXml40dCrPYvhq2YsGmPbHkDEYd8nTHul0inM,17246
numpy/_core/tests/test_memmap.py,sha256=hGXCmU4BQteaAqeuyDA3dVfBsVrSLw2y17CW_GjA0mI,8432
numpy/_core/tests/test_multiarray.py,sha256=iQnPfApmYBJZRlpshROsHiWN7WVn2XSpDvNBXr1AJ3A,409917
numpy/_core/tests/test_multithreading.py,sha256=6lpG9NtpCSUFRyyb2PStwl-_2xcGGtZ2YDYPvL8G2iE,8893
numpy/_core/tests/test_nditer.py,sha256=T2YLb6TRpjMn3PcsSmzsG5MJT_amfimN2LLehxKJ6lE,140066
numpy/_core/tests/test_nep50_promotions.py,sha256=sw60eH2T2EBrpO0VeulZrHpnRCz6wE0MiA4U9B3_MFQ,10355
numpy/_core/tests/test_numeric.py,sha256=WcBq5eN6ba2llB8Pd0c3J5AH5zWnilDJ4T_os660vuI,163642
numpy/_core/tests/test_numerictypes.py,sha256=YZWsJjJSr2wuk7ztIt0Qx3PyGaaXmJTFbp2J4-V0sOo,23893
numpy/_core/tests/test_overrides.py,sha256=aSUDCBrziCFxf0Yz5tz-b5UVovMthH5YkL8Xy2dTqaI,28309
numpy/_core/tests/test_print.py,sha256=MFj_2HDTQqPzsOF6DqDRVuU0fa8xC4tD8NMU7TwCEkg,6987
numpy/_core/tests/test_protocols.py,sha256=b1clvp3Rr7EQ6x8Mtxm9jAiHxPymEU_VJBjwnMingUU,1219
numpy/_core/tests/test_records.py,sha256=WTg9BRbz1yAuj0XaBAi-yggRM5tYWQBFO-rEz8C9vSM,21091
numpy/_core/tests/test_regression.py,sha256=6AfTIt3VsnE9leCsCCaDBfKb4PxocGTQ69dFt95-qV0,98235
numpy/_core/tests/test_scalar_ctors.py,sha256=wg1isOo2orxvVuD65iapPihwecXS1DXxj1eqM5-GaK8,6931
numpy/_core/tests/test_scalar_methods.py,sha256=1qrblm2HsjBvot8mYDh06PaoUjO6_v06GMEfGN7UFKQ,9363
numpy/_core/tests/test_scalarbuffer.py,sha256=0OgVIXWv8iD563F5nvEpIeuxunKn9JJlrinhBtHJvZI,5791
numpy/_core/tests/test_scalarinherit.py,sha256=WVjRrpNkKvQbO2n-joJ7EF6lG1zFcT4pgANAtvT7C5M,2692
numpy/_core/tests/test_scalarmath.py,sha256=kZWj3nO7va4wj1eoN9v3OKdi0qts8rH840WZa8fBvKo,47759
numpy/_core/tests/test_scalarprint.py,sha256=aDQz8rz3KXQ5EBiaIaxmvHpLaIUkZeI-8PoTqZ4kvyM,20108
numpy/_core/tests/test_shape_base.py,sha256=osyeuLczpwfeECA5Q445GPgfu007aDuGTkMlmQYqKDI,31866
numpy/_core/tests/test_simd.py,sha256=rAzkkJJzq8-SJ74k5jZBj77I5R0MRTZq4iRz2UI6w2Q,50164
numpy/_core/tests/test_simd_module.py,sha256=zR-gFTWPB4j66oVM3ULLGSrx8HZvuAIXKcWKplKUGbM,4007
numpy/_core/tests/test_stringdtype.py,sha256=wZYFpXnBeIlWtKKWWQdtnDYifmLNh2hAP1TYfHTT4Ng,57794
numpy/_core/tests/test_strings.py,sha256=zgPWlZzWpa2j5klM0P1_aQUP0L9wNsGudmIr4hsP394,59284
numpy/_core/tests/test_ufunc.py,sha256=WwEZvIgnwgzbU0MGQcF1OutDwSB5xVOMoVgiGCP7mM4,138282
numpy/_core/tests/test_umath.py,sha256=JpP1xNogXOsz_VBGvzBB5j1i17UqOourMWQeElydYNA,198756
numpy/_core/tests/test_umath_accuracy.py,sha256=6Yh-AZtXu2dEAE1r_nwystCwZuwRMAqkiUMLChqM_54,5602
numpy/_core/tests/test_umath_complex.py,sha256=ixTQ1DrSlI_x-iUrsUV8d1bggPIxKA3ArNQiVJP7_ys,24253
numpy/_core/tests/test_unicode.py,sha256=ejiiGuLXWlMK7WyLUYNfLdYYIeoK1cnZcxYFPKrwYGI,13335
numpy/_core/umath.py,sha256=gbS2GakDKOb712WW6bqjQ_k6zWtN9qS2XJajelgDbf8,2190
numpy/_core/umath.pyi,sha256=9o4EBYeibP9abowHQHuo0iuhbUnfTWw5c8utNmKEduo,2840
numpy/_distributor_init.py,sha256=h5_Cq7ItDrt1JZoAh04aO54ZXsXRRkyGoNFFH9T-08U,436
numpy/_distributor_init.pyi,sha256=CSrbSp2YYxHTxlX7R0nT3RpH7EloB1wIvo7YOA7QWy8,28
numpy/_expired_attrs_2_0.py,sha256=HVsnLanxHyyFAC-1eV6Nz2ORAx5x4ZLMh17vfAlxigQ,3905
numpy/_expired_attrs_2_0.pyi,sha256=gs0UfGezUEO3amoLpN2Xsi0hbwozjEJERAkHQfiDvq8,1315
numpy/_globals.py,sha256=1kTi2An0nDeFpBixK20FxDAw15bXLR1iedY1HpKEhIY,3187
numpy/_globals.pyi,sha256=kst3Vm7ZbznOtHsPya0PzU0KbjRGZ8xhMmTNMafvT-4,297
numpy/_pyinstaller/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/__pycache__/__init__.cpython-313.pyc,,
numpy/_pyinstaller/__pycache__/hook-numpy.cpython-313.pyc,,
numpy/_pyinstaller/hook-numpy.py,sha256=bJTm7LIuDHC5QyGTqUWE48gYsRcolKm3naQXoE1o_C4,1398
numpy/_pyinstaller/hook-numpy.pyi,sha256=2Bcwj2FwR3bRdtm26pmpUELEhsiZ58tQv9Q7_1Yp3HU,362
numpy/_pyinstaller/tests/__init__.py,sha256=l38bo7dpp3u1lVMPErlct_5uBLKj35zuS_r35e7c19c,345
numpy/_pyinstaller/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/_pyinstaller/tests/__pycache__/pyinstaller-smoke.cpython-313.pyc,,
numpy/_pyinstaller/tests/__pycache__/test_pyinstaller.cpython-313.pyc,,
numpy/_pyinstaller/tests/pyinstaller-smoke.py,sha256=xt3dl_DjxuzVTPrqmVmMOZm5-24wBG2TxldQl78Xt1g,1175
numpy/_pyinstaller/tests/test_pyinstaller.py,sha256=31zWlvlAC2sfhdew97x8aDvcYUaV3Tc_0CwFk8pgKaM,1170
numpy/_pytesttester.py,sha256=USOh37bWhXAzQLeGD1U63XpDnbkatfr-aiQu-gHkdBA,6529
numpy/_pytesttester.pyi,sha256=AFDYaaIlhSar-JOfoLtQB9Uq1mPXWhre8STg44CVkvA,515
numpy/_typing/__init__.py,sha256=sBjoHDBSeozEDlNiZdE6pt0pcQ37h_oMufU9fwqTKMI,7336
numpy/_typing/__pycache__/__init__.cpython-313.pyc,,
numpy/_typing/__pycache__/_add_docstring.cpython-313.pyc,,
numpy/_typing/__pycache__/_array_like.cpython-313.pyc,,
numpy/_typing/__pycache__/_char_codes.cpython-313.pyc,,
numpy/_typing/__pycache__/_dtype_like.cpython-313.pyc,,
numpy/_typing/__pycache__/_extended_precision.cpython-313.pyc,,
numpy/_typing/__pycache__/_nbit.cpython-313.pyc,,
numpy/_typing/__pycache__/_nbit_base.cpython-313.pyc,,
numpy/_typing/__pycache__/_nested_sequence.cpython-313.pyc,,
numpy/_typing/__pycache__/_scalars.cpython-313.pyc,,
numpy/_typing/__pycache__/_shape.cpython-313.pyc,,
numpy/_typing/__pycache__/_ufunc.cpython-313.pyc,,
numpy/_typing/_add_docstring.py,sha256=Oje462jvQMs5dDxRFWrDiKdK08-5sU-b6WKoSRAg2B4,4152
numpy/_typing/_array_like.py,sha256=N-e4p17RNfe7H3jnpcX0cUXur4AIcjiTi95baW-0EZY,4294
numpy/_typing/_callable.pyi,sha256=2AqAeoO44UxJgQi3I7ZaZER-GdxIoLIyQ35yWWPtO8I,12133
numpy/_typing/_char_codes.py,sha256=VZvjzpRG1Ehf2frndiRLLbPRa59A6FocdwGwjHEOorM,8977
numpy/_typing/_dtype_like.py,sha256=iT-SzDHts4azkS6Yws6WSy0r9uj9HFLVfMrg0AFqT5k,3876
numpy/_typing/_extended_precision.py,sha256=3jaNHY4qJwWODLFWvlfUQROLblfqqFDjOlp8bHnhMBI,449
numpy/_typing/_nbit.py,sha256=pjOpz0sIdhphsXMK0dCQeQWXsrDpxCVZrYJ1wmALf04,651
numpy/_typing/_nbit_base.py,sha256=PnQt_VbBKX_Uj17g_0yfoUqwh0bnN3LEyFHgR6GzNaw,3152
numpy/_typing/_nbit_base.pyi,sha256=0AWGQcWdjpfA7y2w_b_AWdwq1wHOrL3s9lV4uqfhjvI,780
numpy/_typing/_nested_sequence.py,sha256=gZZRCnko04ZbsGaLbAx9VSsvKPR7UuwuzRAxwd1FYX0,2584
numpy/_typing/_scalars.py,sha256=rTil_dSaoBGmmGD9QQZ0NqEP2BeZtkOEK9ZayDMB-l0,964
numpy/_typing/_shape.py,sha256=5csdB-yj390thRrWPnwU7LcVfq-wYnd8QvXyuGdjAX4,283
numpy/_typing/_ufunc.py,sha256=lok5QhQ5aJBARpyVoffrbeuEJsJ5vA6DaJ4aHTeUhms,163
numpy/_typing/_ufunc.pyi,sha256=pEjT3UA4mnynzGD-hqex9aC4wWtEjZJn3UMaCn9KJWs,27516
numpy/_utils/__init__.py,sha256=q3vMrxeBeeU9pvCvLOkodDgzZS5V1jeI1_UZd4BbzDU,3572
numpy/_utils/__init__.pyi,sha256=1M5QCsMaFkyallLWfERBBCw2ByYLRH1SuLFcn0WZ9Ns,756
numpy/_utils/__pycache__/__init__.cpython-313.pyc,,
numpy/_utils/__pycache__/_convertions.cpython-313.pyc,,
numpy/_utils/__pycache__/_inspect.cpython-313.pyc,,
numpy/_utils/__pycache__/_pep440.cpython-313.pyc,,
numpy/_utils/_convertions.py,sha256=vetZFqC1qB-Z9jvc7RKuU_5ETOaSbjhbKa-sVwYV8TU,347
numpy/_utils/_convertions.pyi,sha256=zkZfkdBk6-XcyD3zmr7E5sJbYasvyDCInUtWvrtjVhY,122
numpy/_utils/_inspect.py,sha256=fpHbL1Gx7flw4HHjnNHNN-v8NKx1WgFBWgnX8T5hliY,7628
numpy/_utils/_inspect.pyi,sha256=341-uESK3y_QtWGVDxgquu--jMLoYrWvrFFwVRAt914,2326
numpy/_utils/_pep440.py,sha256=MZ5ZR1-o_4kA-68YcdUfkHkqUf3wRcKxQm08uv2GoE8,14474
numpy/_utils/_pep440.pyi,sha256=LdpDFW8iIj_bLbuTbvRr2XWmC9YS9lrpzLR7efqL2GU,3991
numpy/char/__init__.py,sha256=KAKgke3wwjmEwxfiwkEXehe17DoN1OR_vkLBA9WFaGs,95
numpy/char/__init__.pyi,sha256=XN-Twg_XKK4bMmir1UZ4nCtlW7nOezcU5Ix4N7X4OhQ,1651
numpy/char/__pycache__/__init__.cpython-313.pyc,,
numpy/conftest.py,sha256=iSiBmf_G9XM4jELWnZEAfdnxzbz1XbmIkKj3e46QljE,8835
numpy/core/__init__.py,sha256=mCDTG1UnW38pcRG0sikf7oE2oP4MpO86ndHjquhL85U,1323
numpy/core/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/__pycache__/__init__.cpython-313.pyc,,
numpy/core/__pycache__/_dtype.cpython-313.pyc,,
numpy/core/__pycache__/_dtype_ctypes.cpython-313.pyc,,
numpy/core/__pycache__/_internal.cpython-313.pyc,,
numpy/core/__pycache__/_multiarray_umath.cpython-313.pyc,,
numpy/core/__pycache__/_utils.cpython-313.pyc,,
numpy/core/__pycache__/arrayprint.cpython-313.pyc,,
numpy/core/__pycache__/defchararray.cpython-313.pyc,,
numpy/core/__pycache__/einsumfunc.cpython-313.pyc,,
numpy/core/__pycache__/fromnumeric.cpython-313.pyc,,
numpy/core/__pycache__/function_base.cpython-313.pyc,,
numpy/core/__pycache__/getlimits.cpython-313.pyc,,
numpy/core/__pycache__/multiarray.cpython-313.pyc,,
numpy/core/__pycache__/numeric.cpython-313.pyc,,
numpy/core/__pycache__/numerictypes.cpython-313.pyc,,
numpy/core/__pycache__/overrides.cpython-313.pyc,,
numpy/core/__pycache__/records.cpython-313.pyc,,
numpy/core/__pycache__/shape_base.cpython-313.pyc,,
numpy/core/__pycache__/umath.cpython-313.pyc,,
numpy/core/_dtype.py,sha256=BW-GFvu8BQiN-j6-3mESSWN3IQv9w8wNa3N53lisryI,333
numpy/core/_dtype.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/_dtype_ctypes.py,sha256=pwXec_vp-L06nnzFO66mwjBuPpJPhICecnyfvW2yEMg,361
numpy/core/_dtype_ctypes.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/_internal.py,sha256=i8Uf68tmcvQEmYoRWF3YqnJGCWI3GxZZEAecy5euNqg,976
numpy/core/_multiarray_umath.py,sha256=zstXKBlwOv7q3YjVdb-zn4ypnyyI6fAGc6ahkEBav-g,2155
numpy/core/_utils.py,sha256=dAaZtXVWhOEFiwmVsz8Mn77HsynMDKhZ7HkrjD1Q3vc,944
numpy/core/arrayprint.py,sha256=zsOt7vFu-b1_7rPlKr7iGh12n4CmwzmU_fWV2CedPi4,349
numpy/core/defchararray.py,sha256=4JjDjl62Abk7fNp-HZeuNSQUNNKJddNro6mD6ef4gq8,357
numpy/core/einsumfunc.py,sha256=cW79vhPJJsi2oD-rXc_w5EKq4hlRo1YlyqI3ZnmsMx8,349
numpy/core/fromnumeric.py,sha256=HD3e5PrYjtMOYQCMXTGZlgkikwvYWc1XRuNT5xhSxVg,353
numpy/core/function_base.py,sha256=z5aEiXHQ4AAkHfGLQ8ul_hvjCWL2lDkgPLlWJE_4w-M,361
numpy/core/getlimits.py,sha256=Tut0lg_HyjJXHSfB3c0Z0DvOR7amxUksqI_1MpOlXAo,345
numpy/core/multiarray.py,sha256=nN54eP9dzhnY4oNoVBN2q7yDjF1w7PbMQx4a7oMqTVI,818
numpy/core/numeric.py,sha256=Qev9oaDAGdyblrDjVXBzTY069E8Z-R3PIOnDp3K6XRY,372
numpy/core/numerictypes.py,sha256=CHNOCimC3CarkejHOm-rV7b7bmykIlJAAhXj923pKq0,357
numpy/core/overrides.py,sha256=wETB95vH9MSwFC3rg3GAUGozKJbCuKdVMhS_zC5baUw,345
numpy/core/overrides.pyi,sha256=HScieJk23k4Lk14q8u9CEc3ZEVOQ6hGu_FeWDR2Tyu8,532
numpy/core/records.py,sha256=xWh78TWkPZxZx5VY05Jia8-y1HyIzkEvmrF8BiKBb68,337
numpy/core/shape_base.py,sha256=BWl-Of1Gl8nr0eBguDIdKbS5h9OdmO-VZPUQOe2e62Y,349
numpy/core/umath.py,sha256=XggXI2bTIR9O4U2vyfgoja0kKI2q7sF3dMCWmukWIqQ,329
numpy/ctypeslib/__init__.py,sha256=o9oMM6-vOwS4PVageFyXsh6x23hQtcsemoAVVR3kuHw,206
numpy/ctypeslib/__init__.pyi,sha256=oNbD2M8hnxCO5HikpBaWxqyadbKM7JChbMhStQ12TIU,652
numpy/ctypeslib/__pycache__/__init__.cpython-313.pyc,,
numpy/ctypeslib/__pycache__/_ctypeslib.cpython-313.pyc,,
numpy/ctypeslib/_ctypeslib.py,sha256=ry2JWVuqOh8IjE_m_UIyQoOpWkYoDue2xZAa3Mz8zdc,19682
numpy/ctypeslib/_ctypeslib.pyi,sha256=yFXsjfx3kGV1YGn-FwsYU61CHIfFobM2-x_IO3sMWPs,8329
numpy/doc/__pycache__/ufuncs.cpython-313.pyc,,
numpy/doc/ufuncs.py,sha256=jMnfQhRknVIhgFVS9z2l5oYM8N1tuQtf5bXMBL449oI,5552
numpy/dtypes.py,sha256=cPkS6BLRvpfsUzhd7Vk1L7_VcenWb1nuHuCxc9fYC4I,1353
numpy/dtypes.pyi,sha256=Kx4i1cde8UPFY0gGpPyMRP8WULEn2BEAwKzIVNzLx14,16175
numpy/exceptions.py,sha256=UygorC0Ez5ycvhySLek51IQ_d0RV3u37hFL0JCW3WPc,8047
numpy/exceptions.pyi,sha256=7Dvia_l7dKwL8MjWqIELvwZvDu4XMIJ4MwYjp6aKSaE,776
numpy/f2py/__init__.py,sha256=1sHuSvD-wFPLK6vD_pjY527X4KP8Jlz3bsbqY2ImAaI,2534
numpy/f2py/__init__.pyi,sha256=7mguZLcgQk0DBArDrTfWKMh3Twp4YJ8uNeLxvufDrO0,138
numpy/f2py/__main__.py,sha256=TDesy_2fDX-g27uJt4yXIXWzSor138R2t2V7HFHwqAk,135
numpy/f2py/__pycache__/__init__.cpython-313.pyc,,
numpy/f2py/__pycache__/__main__.cpython-313.pyc,,
numpy/f2py/__pycache__/__version__.cpython-313.pyc,,
numpy/f2py/__pycache__/_isocbind.cpython-313.pyc,,
numpy/f2py/__pycache__/_src_pyf.cpython-313.pyc,,
numpy/f2py/__pycache__/auxfuncs.cpython-313.pyc,,
numpy/f2py/__pycache__/capi_maps.cpython-313.pyc,,
numpy/f2py/__pycache__/cb_rules.cpython-313.pyc,,
numpy/f2py/__pycache__/cfuncs.cpython-313.pyc,,
numpy/f2py/__pycache__/common_rules.cpython-313.pyc,,
numpy/f2py/__pycache__/crackfortran.cpython-313.pyc,,
numpy/f2py/__pycache__/diagnose.cpython-313.pyc,,
numpy/f2py/__pycache__/f2py2e.cpython-313.pyc,,
numpy/f2py/__pycache__/f90mod_rules.cpython-313.pyc,,
numpy/f2py/__pycache__/func2subr.cpython-313.pyc,,
numpy/f2py/__pycache__/rules.cpython-313.pyc,,
numpy/f2py/__pycache__/symbolic.cpython-313.pyc,,
numpy/f2py/__pycache__/use_rules.cpython-313.pyc,,
numpy/f2py/__version__.py,sha256=u3yEZEhZzW9QwLBqzFEO-zZDqsECiHs3ixdOlRnv9Jo,49
numpy/f2py/__version__.pyi,sha256=8GyGk3Z3JL6jXsqXbhheqYSqtp9zqapNanxA7fHf_uA,46
numpy/f2py/_backends/__init__.py,sha256=xIVHiF-velkBDPKwFS20PSg-XkFW5kLAVj5CSqNLddM,308
numpy/f2py/_backends/__init__.pyi,sha256=RC41nCG_RhaOllATOhrOdFFDHGDEErv56plcdVo2GMM,141
numpy/f2py/_backends/__pycache__/__init__.cpython-313.pyc,,
numpy/f2py/_backends/__pycache__/_backend.cpython-313.pyc,,
numpy/f2py/_backends/__pycache__/_distutils.cpython-313.pyc,,
numpy/f2py/_backends/__pycache__/_meson.cpython-313.pyc,,
numpy/f2py/_backends/_backend.py,sha256=9cxRVrA-5wcm2fnVdR-F08sYwGBf89ZZ7bl5VHqKabU,1195
numpy/f2py/_backends/_backend.pyi,sha256=S3xxAntiuMAjkWSQgBX32XiB7AMlwb4SNahYmpUeSG0,1388
numpy/f2py/_backends/_distutils.py,sha256=0SMBqxZgJBhfgX3HW0pEcL3S0qUFVCdSEsTLv1cEcJs,2461
numpy/f2py/_backends/_distutils.pyi,sha256=HHVnI_ozA7-RQIcj-x_DW_crVJPNDSDk6CYVECtHABM,476
numpy/f2py/_backends/_meson.py,sha256=y_ivRspti-g5lmOEhNigRsgF-QPiizM5ljzLTB3lOK4,8338
numpy/f2py/_backends/_meson.pyi,sha256=pwMQO3wxfbwAW7lwOis5F5ExqUITFy514ldtfaQ0W_A,1932
numpy/f2py/_backends/meson.build.template,sha256=6XD3j-K5pc1P_icgUWkrgEsyludQWsqS5rb6UB29tH0,1654
numpy/f2py/_isocbind.py,sha256=QVoR_pD_bY9IgTaSHHUw_8EBg0mkaf3JZfwhLfHbz1Q,2422
numpy/f2py/_isocbind.pyi,sha256=ByVGplEnG_CaErwiRY5khEoIICe4kFGm416sJnIh68s,352
numpy/f2py/_src_pyf.py,sha256=u6eLk_jbxlnY3roCebGAk6wYrYJ79ZQUgl89Ck1uafI,7942
numpy/f2py/_src_pyf.pyi,sha256=DcXllb52JgjmcpIxfdFZS_kO0je9M2xd-FD6-OHDR_k,1041
numpy/f2py/auxfuncs.py,sha256=np0s118cTsP2XIJVdyFbyDMRq-3KF_IbuKQaE3Xt-rQ,27924
numpy/f2py/auxfuncs.pyi,sha256=XSYge_gsndkSEiTYBGblo776kLTnEJFjj8tlNpWw7Cw,8244
numpy/f2py/capi_maps.py,sha256=ncBlu6yE8AVLpMuyTeKL4gVncCeJj708gSXiHxnwgmY,30890
numpy/f2py/capi_maps.pyi,sha256=2b-Sg7dCr0RqxWZ9FmLm4Vgfs9chnuaVWYZCsC4gDFY,1099
numpy/f2py/cb_rules.py,sha256=Ad-tkBGZdwjsPyC4v8zmh7c5v5mIOKybO6k9lNrlf9g,25716
numpy/f2py/cb_rules.pyi,sha256=VYhLJlRKpe2jE2XTKXHmljol-R09YK8tWscZ28pMicI,512
numpy/f2py/cfuncs.py,sha256=V9GZ2E3s0_rragphRwNaCxq5Z3Mvbw-UDBR7wdApbnE,54223
numpy/f2py/cfuncs.pyi,sha256=fWlbI1vH3IdXj3hmrda5Cl_wocO5Fn3uwTUoHBc_6Mg,833
numpy/f2py/common_rules.py,sha256=HJ21QrdclhsGHj883Ab337-bSlPZopPALzXIMNfkT6c,5173
numpy/f2py/common_rules.pyi,sha256=2d2LfXQr_st4cPnCZPQq5_hK9sTqj2436_t7Bf0PiSs,332
numpy/f2py/crackfortran.py,sha256=3uovq4FHMoecI-qTZLi7vdUjFWJRdn94JidlFCHeQ_E,150604
numpy/f2py/crackfortran.pyi,sha256=opsl2XvnH6_N4lh2VdFUGAcxAcipJjGxIKnCy7eIvUI,10534
numpy/f2py/diagnose.py,sha256=-UK2lwqufbuTqSex3w2H4-Qld7Z1NeutRllDNt6TDoA,5224
numpy/f2py/diagnose.pyi,sha256=OEYd5Ceee1VMh-2g_NTOBv3MAaYsAFcK906NPNAejLs,115
numpy/f2py/f2py2e.py,sha256=HmFAAJEHIo3JXUFeneYCdDvdqnEw1dTfuu0l3xlZAx4,29549
numpy/f2py/f2py2e.pyi,sha256=iwXUAdZ55x43Ag45bbo9P49o3LSZJDjvVx0Lz5d9Src,2229
numpy/f2py/f90mod_rules.py,sha256=GjvlboOdjc-lLmC0Tkxa8q_43fkfmo9dbWwv70xN_zI,10079
numpy/f2py/f90mod_rules.pyi,sha256=0LIlPT9YI3Oit8aiP-i_JJPRsczDwzzQ96v14gMS1T0,467
numpy/f2py/func2subr.py,sha256=X073hQWexxQP3aNdsa5EpX_opMsi7u5Cw5c0-LdMDGg,10378
numpy/f2py/func2subr.pyi,sha256=ide-SEoLyEEfa51Wqe6eKRJZvnuNYDJqD7BS0akqqzQ,393
numpy/f2py/rules.py,sha256=yhCJIGoMapOA6GzR6773uPXo7g38maMXpbVp50mVv-g,64720
numpy/f2py/rules.pyi,sha256=YyO6clxow1aWvuZL6z9Qv6I3OdIy0V6imXNOqzD-odQ,1369
numpy/f2py/setup.cfg,sha256=828sy3JvJmMzVxLkC-y0lxcEMaDTnMc3l9dWqP4jYng,50
numpy/f2py/src/fortranobject.c,sha256=BVwVWRyTxvEk45KrKBXggnJGhQUchv5ASM6_4nCnUd8,47792
numpy/f2py/src/fortranobject.h,sha256=uCcHO8mjuANlKb3c7YAZwM4pgT0CTaXWLYqgE27Mnt0,5996
numpy/f2py/symbolic.py,sha256=GqU6BHrwjok_8scjBe9x03sCtOvTvT_6TEw6o8qf1mE,54730
numpy/f2py/symbolic.pyi,sha256=hqioW-cbjohyt71aN3hLlI483xa4y3aZkz5d7uAq9cc,6304
numpy/f2py/tests/__init__.py,sha256=l38bo7dpp3u1lVMPErlct_5uBLKj35zuS_r35e7c19c,345
numpy/f2py/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_abstract_interface.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_array_from_pyobj.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_assumed_shape.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_block_docstring.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_callback.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_character.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_common.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_crackfortran.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_data.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_docs.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_f2cmap.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_f2py2e.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_isoc.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_kind.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_mixed.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_modules.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_parameter.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_pyf_src.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_quoted_character.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_character.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_complex.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_integer.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_logical.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_real.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_routines.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_semicolon_split.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_size.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_string.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_symbolic.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_value_attrspec.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/util.cpython-313.pyc,,
numpy/f2py/tests/src/abstract_interface/foo.f90,sha256=aCaFEqfXp79pVXnTFtjZBWUY_5pu8wsehp1dEauOkSE,692
numpy/f2py/tests/src/abstract_interface/gh18403_mod.f90,sha256=y3R2dDn0BUz-0bMggfT1jwXbhz_gniz7ONMpureEQew,111
numpy/f2py/tests/src/array_from_pyobj/wrapmodule.c,sha256=0UkctY5oeFs9B9qnX8qhe3wTFZA_mF-FBBkJoy_iuQg,7713
numpy/f2py/tests/src/assumed_shape/.f2py_f2cmap,sha256=zfuOShmuotzcLIQDnVFaARwvM66iLrOYzpquIGDbiKU,30
numpy/f2py/tests/src/assumed_shape/foo_free.f90,sha256=fqbSr7VlKfVrBulFgQtQA9fQf0mQvVbLi94e4FTST3k,494
numpy/f2py/tests/src/assumed_shape/foo_mod.f90,sha256=9pbi88-uSNP5IwS49Kim982jDAuopo3tpEhg2SOU7no,540
numpy/f2py/tests/src/assumed_shape/foo_use.f90,sha256=9Cl1sdrihB8cCSsjoQGmOO8VRv9ni8Fjr0Aku1UdEWM,288
numpy/f2py/tests/src/assumed_shape/precision.f90,sha256=3L_F7n5ju9F0nxw95uBUaPeuiDOw6uHvB580eIj7bqI,134
numpy/f2py/tests/src/block_docstring/foo.f,sha256=KVTeqSFpI94ibYIVvUW6lOQ9T2Bx5UzZEayP8Maf2H0,103
numpy/f2py/tests/src/callback/foo.f,sha256=rLqaaaUpWFTaGVxNoGERtDKGCa5dLCTW5DglsFIx-wU,1316
numpy/f2py/tests/src/callback/gh17797.f90,sha256=-_NvQK0MzlSR72PSuUE1FeUzzsMBUcPKsbraHIF7O24,155
numpy/f2py/tests/src/callback/gh18335.f90,sha256=n_Rr99cI7iHBEPV3KGLEt0QKZtItEUKDdQkBt0GKKy4,523
numpy/f2py/tests/src/callback/gh25211.f,sha256=ejY_ssadbZQfD5_-Xnx_ayzWXWLjkdy7DGp6C_uCUCY,189
numpy/f2py/tests/src/callback/gh25211.pyf,sha256=nrzvt2QHZRCcugg0R-4FDMMl1MJmWCOAjR7Ta-pXz7Y,465
numpy/f2py/tests/src/callback/gh26681.f90,sha256=ykwNXWyja5FfZk1bPihbYiMmMlbKhRPoPKva9dNFtLM,584
numpy/f2py/tests/src/cli/gh_22819.pyf,sha256=e3zYjFmiOxzdXoxzgkaQ-CV6sZ1t4aKugyhqRXmBNdQ,148
numpy/f2py/tests/src/cli/hi77.f,sha256=bgBERF4EYxHlzJCvZCJOlEmUE1FIvipdmj4LjdmL_dE,74
numpy/f2py/tests/src/cli/hiworld.f90,sha256=RncaEqGWmsH9Z8BMV-UmOTUyo3-e9xOQGAmNgDv6SfY,54
numpy/f2py/tests/src/common/block.f,sha256=tcGKa42S-6bfA6fybpM0Su_xjysEVustkEJoF51o_pE,235
numpy/f2py/tests/src/common/gh19161.f90,sha256=Vpb34lRVC96STWaJerqkDQeZf7mDOwWbud6pW62Tvm4,203
numpy/f2py/tests/src/crackfortran/accesstype.f90,sha256=3ONHb4ZNx0XISvp8fArnUwR1W9rzetLFILTiETPUd80,221
numpy/f2py/tests/src/crackfortran/common_with_division.f,sha256=JAzHD5aluoYw0jVGZjBYd1wTABU0PwNBD0cz3Av5AAk,511
numpy/f2py/tests/src/crackfortran/data_common.f,sha256=rP3avnulWqJbGCFLWayjoFKSspGDHZMidPTurjz33Tc,201
numpy/f2py/tests/src/crackfortran/data_multiplier.f,sha256=LaPXVuo5lX0gFZVh76Hc7LM1sMk9EBPALuXBnHAGdOA,202
numpy/f2py/tests/src/crackfortran/data_stmts.f90,sha256=MAZ3gstsPqECk3nWQ5Ql-C5udrIv3sAciW1ZGTtHLts,713
numpy/f2py/tests/src/crackfortran/data_with_comments.f,sha256=FUPluNth5uHgyKqjQW7HKmyWg4wDXj3XPJCIC9ZZuOs,183
numpy/f2py/tests/src/crackfortran/foo_deps.f90,sha256=D9FT8Rx-mK2p8R6r4bWxxqgYhkXR6lNmPj2RXOseMpw,134
numpy/f2py/tests/src/crackfortran/gh15035.f,sha256=0G9bmfVafpuux4-ZgktYZ6ormwrWDTOhKMK4wmiSZlQ,391
numpy/f2py/tests/src/crackfortran/gh17859.f,sha256=acknjwoWYdA038oliYLjB4T1PHhXkKRLeJobIgB_Lbo,352
numpy/f2py/tests/src/crackfortran/gh22648.pyf,sha256=xPnKx4RcT1568q-q_O83DYpCgVYJ8z4WQ-yLmHPchJA,248
numpy/f2py/tests/src/crackfortran/gh23533.f,sha256=k2xjRpRaajMYpi5O-cldYPTZGFGB12PUGcj5Fm9joyk,131
numpy/f2py/tests/src/crackfortran/gh23598.f90,sha256=20ukdZXq-qU0Zxzt4W6cO8tRxlNlQ456zgD09zdozCE,105
numpy/f2py/tests/src/crackfortran/gh23598Warn.f90,sha256=FvnIxy5fEOvzNb5WSkWzPk7yZ9yIv0yPZk9vNnS-83w,216
numpy/f2py/tests/src/crackfortran/gh23879.f90,sha256=jELVfEGEF66z_Pv_iBHp3yGsGhadB0dnKCDtPcaz_CM,352
numpy/f2py/tests/src/crackfortran/gh27697.f90,sha256=mTOEncxZlam6N-3I-IL0ua-iLkgqDrrVXNsE-7y7jAM,376
numpy/f2py/tests/src/crackfortran/gh2848.f90,sha256=-IpkeTz0j9_lkQeN9mT7w3U1cAJjQxSMdAmyHdF8oVg,295
numpy/f2py/tests/src/crackfortran/operators.f90,sha256=cb1JO2hIMCQejZO_UJWluBCP8LdXQbBJw2XN6YHB3JA,1233
numpy/f2py/tests/src/crackfortran/privatemod.f90,sha256=9O2oWEquIUcbDB1wIzNeae3hx4gvXAoYW5tGfBt3KWk,185
numpy/f2py/tests/src/crackfortran/publicmod.f90,sha256=nU_VXCKiniiUq_78KAWkXiN6oiMQh39emMxbgOVf9cg,177
numpy/f2py/tests/src/crackfortran/pubprivmod.f90,sha256=-uz75kquU4wobaAPZ1DLKXJg6ySCZoDME1ce6YZ2q5Y,175
numpy/f2py/tests/src/crackfortran/unicode_comment.f90,sha256=wDMoF7F7VFYdeocfTyWIh7noniEwExVb364HrhUSbSg,102
numpy/f2py/tests/src/f2cmap/.f2py_f2cmap,sha256=fwszymaWhcWO296u5ThHW5yMAkFhB6EtHWqqpc9FAVI,83
numpy/f2py/tests/src/f2cmap/isoFortranEnvMap.f90,sha256=rphN_mmzjCCCkdPM0HjsiJV7rmxpo4GoCNp5qmBzv8U,307
numpy/f2py/tests/src/isocintrin/isoCtests.f90,sha256=Oir0PfE3mErnUQ42aFxiqAkcYn3B6b1FHIPGipDdekg,1032
numpy/f2py/tests/src/kind/foo.f90,sha256=6_zq3OAWsuNJ5ftGTQAEynkHy-MnuLgBXmMIgbvL7yU,367
numpy/f2py/tests/src/mixed/foo.f,sha256=Zgn0xDhhzfas3HrzgVSxIL1lGEF2mFRVohrvXN1thU0,90
numpy/f2py/tests/src/mixed/foo_fixed.f90,sha256=6eEEYCH71gPp6lZ6e2afLrfS6F_fdP7GZDbgGJJ_6ns,187
numpy/f2py/tests/src/mixed/foo_free.f90,sha256=UC6iVRcm0-aVXAILE5jZhivoGQbKU-prqv59HTbxUJA,147
numpy/f2py/tests/src/modules/gh25337/data.f90,sha256=EqMEuEV0_sx4XbFzftbU_6VfGtOw9Tbs0pm0eVEp2cA,188
numpy/f2py/tests/src/modules/gh25337/use_data.f90,sha256=DChVLgD7qTOpbYNmfGjPjfOx5YsphMIYwdwnF12X4xM,185
numpy/f2py/tests/src/modules/gh26920/two_mods_with_no_public_entities.f90,sha256=MMLPSzBwuGS4UwCXws9djH11F5tG5xFLc80CDb4U9Mk,423
numpy/f2py/tests/src/modules/gh26920/two_mods_with_one_public_routine.f90,sha256=1dJD1kDC_wwn7v_zF49D3n62T1x9wFxGKanQQz_VI7k,424
numpy/f2py/tests/src/modules/module_data_docstring.f90,sha256=-asnMH7vZMwVIeMU2YiLWgYCUUUxZgPTpbAomgWByHs,236
numpy/f2py/tests/src/modules/use_modules.f90,sha256=bveSAqXIZtd4NMlDfFei1ZlesFAa9An5LjkD-gDk2ms,418
numpy/f2py/tests/src/negative_bounds/issue_20853.f90,sha256=IxBGWem-uv9eHgDhysEdGTmNKHR1gAiU7YJPo20eveM,164
numpy/f2py/tests/src/parameter/constant_array.f90,sha256=fkYemwIBKsP63-FGKBW8mzOAp6k13eZOin8sQe1pyno,1513
numpy/f2py/tests/src/parameter/constant_both.f90,sha256=L0rG6-ClvHx7Qsch46BUXRi_oIEL0uw5dpRHdOUQuv0,1996
numpy/f2py/tests/src/parameter/constant_compound.f90,sha256=lAT76HcXGMgr1NfKof-RIX3W2P_ik1PPqkRdJ6EyBmM,484
numpy/f2py/tests/src/parameter/constant_integer.f90,sha256=42jROArrG7vIag9wFa_Rr5DBnnNvGsrEUgpPU14vfIo,634
numpy/f2py/tests/src/parameter/constant_non_compound.f90,sha256=u9MRf894Cw0MVlSOUbMSnFSHP4Icz7RBO21QfMkIl-Q,632
numpy/f2py/tests/src/parameter/constant_real.f90,sha256=QoPgKiHWrwI7w5ctYZugXWzaQsqSfGMO7Jskbg4CLTc,633
numpy/f2py/tests/src/quoted_character/foo.f,sha256=0zXQbdaqB9nB8R4LF07KDMFDbxlNdiJjVdR8Nb3nzIM,496
numpy/f2py/tests/src/regression/AB.inc,sha256=ydjTVb6QEw1iYw2tRiziqqzWcDHrJsNWr3m51-rqFXQ,17
numpy/f2py/tests/src/regression/assignOnlyModule.f90,sha256=vPJbhOlNsLrgN3su4ohHUSbxE4GGKU7SiJh7dhBvX3o,633
numpy/f2py/tests/src/regression/datonly.f90,sha256=HuBLuEw0kNEplJ9TxxSNr7hLj-jx9ZNGaXC8iLm_kf8,409
numpy/f2py/tests/src/regression/f77comments.f,sha256=FjP-07suTBdqgtwiENT04P-47UB4g9J5-20IQdXAHhM,652
numpy/f2py/tests/src/regression/f77fixedform.f95,sha256=KdKFcAc3ZrID-h4nTOJDdEYfQzR2kkn9VqQCorfJGpM,144
numpy/f2py/tests/src/regression/f90continuation.f90,sha256=VweFIi5-xxZhtgSOh8i_FjMPXu_od9qjrDHq6ma5X5k,285
numpy/f2py/tests/src/regression/incfile.f90,sha256=gq87H2CtCZUON9V5UzcK6x_fthnWDVuPFQLa0fece1M,97
numpy/f2py/tests/src/regression/inout.f90,sha256=TlMxJjhjjiuLI--Tg2LshLnbfZpiKz37EpR_tPKKSx8,286
numpy/f2py/tests/src/regression/lower_f2py_fortran.f90,sha256=bWlj2Frch3onnUpd6DTaoLDa6htrrbkBiI9JIRbQPfE,105
numpy/f2py/tests/src/regression/mod_derived_types.f90,sha256=Cb9WV1sxoKt2wJCl1Z9QR42iLYX226f_boX-_ehDLAQ,589
numpy/f2py/tests/src/return_character/foo77.f,sha256=tRyQSu9vNWtMRi7gjmMN-IZnS7ogr5YS0n38uax_Eo0,1025
numpy/f2py/tests/src/return_character/foo90.f90,sha256=WPQZC6CjXLbUYpzy5LItEoHmRDFxW0ABB3emRACsjZU,1296
numpy/f2py/tests/src/return_complex/foo77.f,sha256=7-iKoamJ-VObPFR-Tslhiw9E-ItIvankWMyxU5HqxII,1018
numpy/f2py/tests/src/return_complex/foo90.f90,sha256=_GOKOZeooWp3pEaTBrZNmPmkgGodj33pJnJmySnp7aE,1286
numpy/f2py/tests/src/return_integer/foo77.f,sha256=EKs1KeAOQBkIO99tMCx0H7_lpqvqpjie8zWZ6T_bAR4,1234
numpy/f2py/tests/src/return_integer/foo90.f90,sha256=0aYWcaAVs7Lw3Qbf8hupfLC8YavRuPZVIwjHecIlMOo,1590
numpy/f2py/tests/src/return_logical/foo77.f,sha256=Ax3tBVNAlxFtHhV8fziFcsTnoa8YJdapecMr6Qj7fLk,1244
numpy/f2py/tests/src/return_logical/foo90.f90,sha256=IZXCerFecYT24zTQ_spIoPr6n-fRncaM0tkTs8JqO1E,1590
numpy/f2py/tests/src/return_real/foo77.f,sha256=3nAY1YtzGk4osR2jZkHMVIUHxFoOtF1OLfWswpcV7kA,978
numpy/f2py/tests/src/return_real/foo90.f90,sha256=38ZCnBGWb9arlJdnVWvZjVk8uesrQN8wG2GrXGcSIJs,1242
numpy/f2py/tests/src/routines/funcfortranname.f,sha256=ruyXK6eQSLQnQ_rODT1qm1cJvpHrFhI6NRrnWvEIK0U,128
numpy/f2py/tests/src/routines/funcfortranname.pyf,sha256=EgRw8ZWGdd2uK4qCZD89r9VQtEXmnKDx59OpB0K58as,451
numpy/f2py/tests/src/routines/subrout.f,sha256=35DjHIj85ZLkxRxP4bs-WFTQ5y1AyDqBKAXTzSSTAxE,94
numpy/f2py/tests/src/routines/subrout.pyf,sha256=xT_WnDpvpyPb0FMRAVTRRgm3nlfALf1Ojg8x3qZNv_4,332
numpy/f2py/tests/src/size/foo.f90,sha256=nK_767f1TtqVr-dMalNkXmcKbSbLCiabhRkxSDCzLz0,859
numpy/f2py/tests/src/string/char.f90,sha256=X_soOEV8cKsVZefi3iLT7ilHljjvJJ_i9VEHWOt0T9Y,647
numpy/f2py/tests/src/string/fixed_string.f90,sha256=tCN5sA6e7M1ViZtBNvTnO7_efk7BHIjyhFKBoLC3US0,729
numpy/f2py/tests/src/string/gh24008.f,sha256=Z6cq8SFGvmaA72qeH9tu1rP8pYjqm0ONpHn7nGbhoLA,225
numpy/f2py/tests/src/string/gh24662.f90,sha256=xJkiYvrMT9Ipb9Cq7OXl1Ev6TISl8pq1MGemySzfGd0,204
numpy/f2py/tests/src/string/gh25286.f90,sha256=lqEl81Iu9GIDTAbOfkkNGcGgDyyGnPB44mJw2iK1kng,318
numpy/f2py/tests/src/string/gh25286.pyf,sha256=wYkkr5gEN9_RtGjpqh28X1k8KCgh0-Ds9XAt8IC9j4A,393
numpy/f2py/tests/src/string/gh25286_bc.pyf,sha256=ZRvgSzRlaPEx8GyNt97FrRhtCg-r4ZTEDsHNBfit4m8,396
numpy/f2py/tests/src/string/scalar_string.f90,sha256=U1QqVgbF1DbxdFekRjchyDlFRPnXwzG72kuE8A44Za8,185
numpy/f2py/tests/src/string/string.f,sha256=JCwLuH21Ltag5cw_9geIQQJ4Hv_39NqG8Dzbqj1eDKE,260
numpy/f2py/tests/src/value_attrspec/gh21665.f90,sha256=MbbSUQI5Enzq46KWFHRzQbY7q6ZHJH_9NRL-C9i13Wg,199
numpy/f2py/tests/test_abstract_interface.py,sha256=2fTmp5-yLaNKtWvP0jQ6_kqkyWI73kgjIl7Ara25cII,837
numpy/f2py/tests/test_array_from_pyobj.py,sha256=L8QW78M-Mlw_wg6JB3JQb7LEHKBg9o2WIReybiQ6VYI,24374
numpy/f2py/tests/test_assumed_shape.py,sha256=WaIBz38eV2AzRwOvTvTaRkNks8c3H_61TGKtAReP6gk,1517
numpy/f2py/tests/test_block_docstring.py,sha256=DOTSbdInRJCunaEycMGWQUy0b5rIeugPxUKmNg8FA34,604
numpy/f2py/tests/test_callback.py,sha256=uVRfXR6q4ukZfbRUBCnc1cvKIEiaPFr6gc3mrgMzt1w,7362
numpy/f2py/tests/test_character.py,sha256=dLj5WhKbP5CYnuQMsB3uWV5pcgoQCEscujuBSCXwkwA,22572
numpy/f2py/tests/test_common.py,sha256=r_nJN4ZCZ3DstAadAoO_R_igybcb5zxwOJsv_-fRBa8,667
numpy/f2py/tests/test_crackfortran.py,sha256=QNZ9VI61XDF5KoO8r9012AaGaVHzM7g1e2UVK0A4uUU,16834
numpy/f2py/tests/test_data.py,sha256=XBQTj0WqR-XWHuRhT1PWXankrhlNxeqC6S03XA2b7AI,2966
numpy/f2py/tests/test_docs.py,sha256=E2YyLALHZAWJcy9ILwJf619WXTvP8djReDqfo8mkOe8,1919
numpy/f2py/tests/test_f2cmap.py,sha256=hyzKOv261wPDWAmpuidAVFK3x9WXm64U_r3E_bmxNXg,404
numpy/f2py/tests/test_f2py2e.py,sha256=qbPTOzrmJuY32MToMkrlbufkq_G2asp4epdtRFyrSd4,28798
numpy/f2py/tests/test_isoc.py,sha256=KK4VeoPhjF658msdnRYCWlzagFEea8h5SzdjB5FaDk0,1490
numpy/f2py/tests/test_kind.py,sha256=CRKxvcRUJPHg9N4HURLUixtr1sD0bvoABpzl_U14T9Q,1878
numpy/f2py/tests/test_mixed.py,sha256=3J9eftoqFIXViiqd21Ej02eNztjbIuzaqb_2OZYZUSw,897
numpy/f2py/tests/test_modules.py,sha256=rbm9cPZilhdIuFA6rxqtRlPtyd0_IxKoFlB8VFA-1Vc,2384
numpy/f2py/tests/test_parameter.py,sha256=wwyq8vA5FFljYfF55srLYqFvJ-ybdG2vEpr2CRdPqVs,4763
numpy/f2py/tests/test_pyf_src.py,sha256=Tg8PzypY1P2pda3k2VbuKX97VCD7CtdgyJajP-U7AIc,1177
numpy/f2py/tests/test_quoted_character.py,sha256=kvjgkp3bIP2lnkZ2MzS2yB5ytEJfHZydz24VAqs3UKM,495
numpy/f2py/tests/test_regression.py,sha256=ZCuszi9wHhPt9fePqGIY-f4BCPLMF5AfPJUE52_bwKE,6384
numpy/f2py/tests/test_return_character.py,sha256=bdryZo5fXfTUE35M3_8gDqPCa9RKtkxROqBcnTjuwYo,1582
numpy/f2py/tests/test_return_complex.py,sha256=1Nb6IsRfzHTCCiBTU5nHoY36w--Uh2goTedK-d8Xrc4,2507
numpy/f2py/tests/test_return_integer.py,sha256=TsYkKMg1iJ2dBxenxgpHFGYCE1NTnrV4pJLqXbdgyxk,1868
numpy/f2py/tests/test_return_logical.py,sha256=ob4-_KkwWohpwF45SWKxiCKtktReLuqg4WQ8YFcMOQ0,2113
numpy/f2py/tests/test_return_real.py,sha256=c_7uRnUVigz4YcCTuiayZ1Sk2-txxk8JkQWZurC8lAQ,3382
numpy/f2py/tests/test_routines.py,sha256=6hOB8Rn4M-MCgDhKMoqI3YIn274wyWBzaLTjrJrTYCw,824
numpy/f2py/tests/test_semicolon_split.py,sha256=5K_jZ2rJLxvwRDQu0gd_yiOEKiJmngvrH8vLeqsbC0Y,1702
numpy/f2py/tests/test_size.py,sha256=D-7AOZtUn0DekZr-K753WQIcnt0prEdLigSuzjEicFg,1200
numpy/f2py/tests/test_string.py,sha256=X6ECwK-mh-0Dfp8Pcag-jWxidoXtt009QDfjc8sfGT8,3038
numpy/f2py/tests/test_symbolic.py,sha256=qPQoebN4aUJAeLaRpVXvEJ_n4c8WNVkTNStYUtQ6Tg8,18837
numpy/f2py/tests/test_value_attrspec.py,sha256=P3ypxCXsakygnP1IdXH1hzw5VaYKGN40z3SOwK61IPU,345
numpy/f2py/tests/util.py,sha256=YVm0U_jGp_LRO7k4FszcuC5AqoNsnf-cc564vEljABk,12554
numpy/f2py/use_rules.py,sha256=MX3S-9SkSznXTwWWwaccMBhhyE_ZyoG3MCLLnsXXHEE,3475
numpy/f2py/use_rules.pyi,sha256=J7S58xd70JkQBKtzl01T3uKmGfskGO6TiosmxUMW62Y,433
numpy/fft/__init__.py,sha256=j8Qwnr2UUY5ka45gSGF13BN3arX80H8ShOYiAGbucJ0,8506
numpy/fft/__init__.pyi,sha256=z-kDsNgijEpHw8kIdSs4qZtzpSm9cFo0gvK32Q2rTRk,557
numpy/fft/__pycache__/__init__.cpython-313.pyc,,
numpy/fft/__pycache__/_helper.cpython-313.pyc,,
numpy/fft/__pycache__/_pocketfft.cpython-313.pyc,,
numpy/fft/__pycache__/helper.cpython-313.pyc,,
numpy/fft/_helper.py,sha256=VpGmqY4O7zZWm0vg72mGu3AsZ-bca_mVG-olxoJYmmI,7022
numpy/fft/_helper.pyi,sha256=6RxP5wS_vBokkLR6BRXsJqFTTWUasHLEib61hdwa_qU,1439
numpy/fft/_pocketfft.py,sha256=Hycd7Y2xAT5zFnVys14Ker59o1PsVBXjEevXkVabLa8,64291
numpy/fft/_pocketfft.pyi,sha256=wmEYOawqlFBz3Jw5mWMhWm0JQGtY3AO0xCBDpPgtnSc,3312
numpy/fft/_pocketfft_umath.cp313-win_amd64.lib,sha256=alQutmqGWTyEXMxrgqK2E5c1h5uROwr2BN5VfJNcLFY,2176
numpy/fft/_pocketfft_umath.cp313-win_amd64.pyd,sha256=bRU69Gq7E643zerQvIwDzfWAbRpJy20WYvQTIm4TnA8,282112
numpy/fft/helper.py,sha256=ljwFnw440UcYr4CPA8fJn_gC0c3XapfFwrylzDq9vsw,628
numpy/fft/helper.pyi,sha256=MDJI7k0BFz8N1DuYkyBCEdaT09d3CHEsBaG9JAgs2aI,913
numpy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/fft/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/fft/tests/__pycache__/test_helper.cpython-313.pyc,,
numpy/fft/tests/__pycache__/test_pocketfft.cpython-313.pyc,,
numpy/fft/tests/test_helper.py,sha256=Yff4EXasyH-nD6dyEcZfX4g61ZjQcAY7XHfQLnXI1EY,6321
numpy/fft/tests/test_pocketfft.py,sha256=KUUhpoJglZy8-iEYMIprmHG6GdtVv8EED2xJoV2cYs8,25035
numpy/lib/__init__.py,sha256=sZL_BFMWHTRmMAUWlbBJ-ngMCdk6UJ9FdU3n0S0SMEg,3101
numpy/lib/__init__.pyi,sha256=Pg_iljZ5KSCpidhTq-iOKFwLpbiZQWXjI-_ulLsLkwA,579
numpy/lib/__pycache__/__init__.cpython-313.pyc,,
numpy/lib/__pycache__/_array_utils_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_arraypad_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_arraysetops_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_arrayterator_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_datasource.cpython-313.pyc,,
numpy/lib/__pycache__/_format_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_function_base_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_histograms_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_index_tricks_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_iotools.cpython-313.pyc,,
numpy/lib/__pycache__/_nanfunctions_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_npyio_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_polynomial_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_scimath_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_shape_base_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_stride_tricks_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_twodim_base_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_type_check_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_ufunclike_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_user_array_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_utils_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_version.cpython-313.pyc,,
numpy/lib/__pycache__/array_utils.cpython-313.pyc,,
numpy/lib/__pycache__/format.cpython-313.pyc,,
numpy/lib/__pycache__/introspect.cpython-313.pyc,,
numpy/lib/__pycache__/mixins.cpython-313.pyc,,
numpy/lib/__pycache__/npyio.cpython-313.pyc,,
numpy/lib/__pycache__/recfunctions.cpython-313.pyc,,
numpy/lib/__pycache__/scimath.cpython-313.pyc,,
numpy/lib/__pycache__/stride_tricks.cpython-313.pyc,,
numpy/lib/__pycache__/user_array.cpython-313.pyc,,
numpy/lib/_array_utils_impl.py,sha256=JQE9Ul515em350VcRXQaO4BmE0HwIRPdBpFNXu4Hnws,1759
numpy/lib/_array_utils_impl.pyi,sha256=N_DF0J-yyE6S2fLU9Q8hlNt4aeOPQqMmwJmYb49OCHY,846
numpy/lib/_arraypad_impl.py,sha256=roh0MMZ_rZ56kxP5UscumgwSIAMFaacyYWLfK03nR3U,33186
numpy/lib/_arraypad_impl.pyi,sha256=1D9_Ygmy4m8whKV2VH68c4edtsEtCGuDyIlQ1dGnb0g,1926
numpy/lib/_arraysetops_impl.py,sha256=0RK-matE1Xt4ZC_kYG-LBAAiVJAi0xm86maonB-6XuY,42535
numpy/lib/_arraysetops_impl.pyi,sha256=oLykyuQG3SPJz1NzsF1F7nzfozcQ-ZCIEX2IhL0A4EA,13247
numpy/lib/_arrayterator_impl.py,sha256=HUtCLBXcG7mC5AX3KuJcDjHD9FEPheCcHypT6PtSswY,7442
numpy/lib/_arrayterator_impl.pyi,sha256=mpL7ZTlEYRK8C4GbZpqDxIM0QeDNZZgW0MOYF3lVF6g,1922
numpy/lib/_datasource.py,sha256=csFfOVL00V76KM--z9wROAImHDPB5l5b-8h1AJli53c,23431
numpy/lib/_datasource.pyi,sha256=a_mEw94cyK-Ik7ZaQIDIJp8CB2pYV-1FEvRkZHCM20c,1027
numpy/lib/_format_impl.py,sha256=bpsctt6JhoYaUBh8dFUhgyumRXyIaU3aYEOvzQB8wrM,37901
numpy/lib/_format_impl.pyi,sha256=FjQ_zPqG7xOCdWa8B6NYVtjm-xo2oExGvKUYMwZfX54,895
numpy/lib/_function_base_impl.py,sha256=fxFsyEJNOWlzw98bunZJItyPXKK1zfKNKuzytQrfRSk,202153
numpy/lib/_function_base_impl.pyi,sha256=u9JLP6HWWmIblWFddGOlArih3wpAj642rMYRgrCjL84,25101
numpy/lib/_histograms_impl.py,sha256=2dceYr7BOVTkypST4CVgskJzjI-ka_FrL5oxBW7rHEQ,39517
numpy/lib/_histograms_impl.pyi,sha256=PMEsE_oCjEs5jMfkEQhqBJCgadfghsK0sIBPDgfVPnQ,1143
numpy/lib/_index_tricks_impl.py,sha256=IDH9jP2WpgOaGCtcZgLx5eF1FV-eFuVomhVKjCqzyzk,33253
numpy/lib/_index_tricks_impl.pyi,sha256=g28pCOC5G0Vq3V9NG3ba_KDdVJyYBK-V94McQlr1e_w,6521
numpy/lib/_iotools.py,sha256=Yh7xIu5OnSNnj2aX-yW8viJNHNC33nhCDZtaep8TszE,31776
numpy/lib/_iotools.pyi,sha256=QhPWjfhxdtiz73gewLi4Ji_YULmz1bqCa9b6Rmx_AZg,3507
numpy/lib/_nanfunctions_impl.py,sha256=qpA4DEl7ixLa8jPiTHlOgJRrdLr-A29eAUc_F4h7nNI,73973
numpy/lib/_nanfunctions_impl.pyi,sha256=5K8nYwWh2IpzkmC8mHmlHygzfS6Sf5K3EA5NzBCQAMA,885
numpy/lib/_npyio_impl.py,sha256=G4rTZ85uHNKF965_zE9VgPHk_Hx5J-tYK6znSaB4S4w,101876
numpy/lib/_npyio_impl.pyi,sha256=uSzRuJLRG7IJhwbmUDKIq52Q-C4jp4K-pUW-pjrm6Yk,9689
numpy/lib/_polynomial_impl.py,sha256=Z7K4kuUiMaAv4bRoSf2589uznTWUEYS1Mp29vqgyXl8,45599
numpy/lib/_polynomial_impl.pyi,sha256=Ajj0Azu9rnIDONhqX0aWKf_K7kGO37TN-lyq60lhlw4,7315
numpy/lib/_scimath_impl.py,sha256=n7oV3g6IX9sClfv4xJ8UCCtgHxScoycnpjj2iASQ6Xc,16334
numpy/lib/_scimath_impl.pyi,sha256=3-C37vHfGAP84SLbuxHrPAO8ZQ-vgTSXTyh5EjNHXh0,2867
numpy/lib/_shape_base_impl.py,sha256=KKZWJjfeFHSsWZ1mP2ZRAmjuNhK8u1y0RPX9kx1Q25o,40780
numpy/lib/_shape_base_impl.pyi,sha256=RfYQA-3qd4yfZx0OX-uCzYou8LPLjMbmZBoFE413jOU,5647
numpy/lib/_stride_tricks_impl.py,sha256=0Lrnvmponu10hS2g6E0Ec7sHuNrfNS5CoPZPqWPP74M,18574
numpy/lib/_stride_tricks_impl.pyi,sha256=lOmkPGsJBBTsgTpNZcXfrMBd-ljjhhdMB3A700xUi6g,1889
numpy/lib/_twodim_base_impl.py,sha256=ENAC8HiRXnufO7s0A-TKdTe0DBaXQ-LNnEikTTDbpXM,35126
numpy/lib/_twodim_base_impl.pyi,sha256=3FAkKHv0WBeFlL3oL89jl4m259m9zyXZ-3KbsZXxQGM,11631
numpy/lib/_type_check_impl.py,sha256=e_lIw0N7vWLRDAUkG4zFAhEXAWBGh9Fwop6nqWZ8fTg,19920
numpy/lib/_type_check_impl.pyi,sha256=lWM1KaQ_KE3ipEvoegfq8fyTRlc9FCP7P-fFlqDpU8c,10063
numpy/lib/_ufunclike_impl.py,sha256=mq924a_rI7wvsWoPKHyc38WLI11fxCAiog-k6gJ5br0,6516
numpy/lib/_ufunclike_impl.pyi,sha256=cugUHwfpO9bzvD1FHDuTbJDLx69ZzLTHhXkeVf9AaVU,1355
numpy/lib/_user_array_impl.py,sha256=6JeHIbL_loRsIAspm2srJuUm5xxff9tWoCNeSKfQNNc,7996
numpy/lib/_user_array_impl.pyi,sha256=U1_Z_KXyJVnPFkgiVb9VO5xgaMRydB7LgtAPxaLHYew,9335
numpy/lib/_utils_impl.py,sha256=3tqdoP3LcWZLviXW7RSSSuju7o2unlXn3AxJuofdiMk,24125
numpy/lib/_utils_impl.pyi,sha256=zT3cXJBOVTxkmBbaDiWhqxaS4BgemF1HwP4mcfKEmKA,381
numpy/lib/_version.py,sha256=Lt36UNePd_WZKslmmB6RBMrk0-38WApoFM-YGnP1WZE,5005
numpy/lib/_version.pyi,sha256=zAmfNnFeke7_lHsvR94fafNBcuJHpZ1jaB2PyzEostc,658
numpy/lib/array_utils.py,sha256=zmrUIVleWEWzl9XjEpUlDUQHt9qbbsytyu_jLj-OgnE,151
numpy/lib/array_utils.pyi,sha256=1EeFoIjayC8IAWQgNVheBxQeQDJNDklqHSQU_bxLiEo,308
numpy/lib/format.py,sha256=ii8MRQmPZ1nAaqnMqeYtKgdNRP52iZsHPo5rODl20UM,501
numpy/lib/format.pyi,sha256=hjsmMm6tPXHcBSpUjTMnHzqcPdqsZ8X5X_LWDvmdPgk,1548
numpy/lib/introspect.py,sha256=TuhUIVWvWAODqn1tkwqKHN7rNHJhjcMMQyiC_vnUpRQ,2844
numpy/lib/introspect.pyi,sha256=IsntuFrlFhRBZcGGhRUTAgnONUHEbYw_2ApPmffx8QE,155
numpy/lib/mixins.py,sha256=sET7o_pJOco4Bdj66L9lRmkdzr44cDTjvCX7eISjJHk,7380
numpy/lib/mixins.pyi,sha256=NwXdoCR4xu-hlhKPhBa7geYN3Py_Wxb8mMl5tRLThw8,3206
numpy/lib/npyio.py,sha256=EY5_tqGplRo-B3cJtqvf9HC34nQKr5JNgyykHWj5q_E,69
numpy/lib/npyio.pyi,sha256=QIrW_Qz_-9L-ecPo39P0-UiGec2EBPzBC_yJ9MZ1gF0,201
numpy/lib/recfunctions.py,sha256=7to2wo6f8bxxUfVzHrMbo0gR9FNly3zdJK3jRirqrfE,61220
numpy/lib/recfunctions.pyi,sha256=IKl2KsmbQzRYxff2DsiGqOuja_SftP9Ce2q8maqGxwc,13651
numpy/lib/scimath.py,sha256=3nsYqFdGoo0danaHnGK8Qrz0AAA31THUX0qnQsp5eu8,182
numpy/lib/scimath.pyi,sha256=qAJQtJdCacZcUZ0TYxRKBdFiInqtfmu-1nFlrFk1GMc,542
numpy/lib/stride_tricks.py,sha256=qXan9_UpXFAoDLBAaJ1wYb0B86DgP48ogp5sUu3s1Lw,89
numpy/lib/stride_tricks.pyi,sha256=jh8Y0ulZF8CiIWDY_yv-LckvcVh5hMwN5WQEMlBiiyw,176
numpy/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/lib/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test__datasource.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test__iotools.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test__version.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_array_utils.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_arraypad.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_arraysetops.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_arrayterator.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_format.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_function_base.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_histograms.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_index_tricks.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_io.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_loadtxt.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_mixins.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_nanfunctions.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_packbits.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_polynomial.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_recfunctions.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_shape_base.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_stride_tricks.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_twodim_base.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_type_check.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_ufunclike.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_utils.cpython-313.pyc,,
numpy/lib/tests/data/py2-np0-objarr.npy,sha256=ZLoI7K3iQpXDkuoDF1Ymyc6Jbw4JngbQKC9grauVRsk,258
numpy/lib/tests/data/py2-objarr.npy,sha256=F4cyUC-_TB9QSFLAo2c7c44rC6NUYIgrfGx9PqWPSKk,258
numpy/lib/tests/data/py2-objarr.npz,sha256=xo13HBT0FbFZ2qvZz0LWGDb3SuQASSaXh7rKfVcJjx4,366
numpy/lib/tests/data/py3-objarr.npy,sha256=7mtikKlHXp4unZhM8eBot8Cknlx1BofJdd73Np2PW8o,325
numpy/lib/tests/data/py3-objarr.npz,sha256=vVRl9_NZ7_q-hjduUr8YWnzRy8ESNlmvMPlaSSC69fk,453
numpy/lib/tests/data/python3.npy,sha256=X0ad3hAaLGXig9LtSHAo-BgOvLlFfPYMnZuVIxRmj-0,96
numpy/lib/tests/data/win64python2.npy,sha256=agOcgHVYFJrV-nrRJDbGnUnF4ZTPYXuSeF-Mtg7GMpc,96
numpy/lib/tests/test__datasource.py,sha256=L8nGVQKsnz6JMzA22QgBd_dgNY9bogVqziw2NaG0HWQ,10933
numpy/lib/tests/test__iotools.py,sha256=zilzDE3_QME4evTTuSzyBHRcZkH24dRD42vruEr5VZQ,14125
numpy/lib/tests/test__version.py,sha256=I6-cyr_7w1TUvC25hR1gTE3x8S-QRAXRsB4IjPxY3tg,2063
numpy/lib/tests/test_array_utils.py,sha256=gRsql9I0f7RQk-1b8iPa1jDZsd_auCF05-ulxBCVfN4,1150
numpy/lib/tests/test_arraypad.py,sha256=_fnhQ1y-L1jo51aLwQV-9MPiBL723STQLINrfkvjOT8,57570
numpy/lib/tests/test_arraysetops.py,sha256=oebnQ4hWZEBn4E9y2FShpkcsQT-K7ZuHSxk-ndpAITQ,41519
numpy/lib/tests/test_arrayterator.py,sha256=N5tsHwFyL_jnyTJ_nnoE4cHYKzR4fO1frdLqr09Ip0I,1347
numpy/lib/tests/test_format.py,sha256=WfMDB563BYh-sWizN-IGU8wkVDd4OQn7YiyZmskU_88,43010
numpy/lib/tests/test_function_base.py,sha256=-gwyTVr_1xCH8kNBDofqhi52iGQNYhg6m1mwDFuvDJY,175320
numpy/lib/tests/test_histograms.py,sha256=vAPUJdT6J0pGXiFgrhf3TvKRHox1sgC1Ag7SNntbg3M,34821
numpy/lib/tests/test_index_tricks.py,sha256=lpkqcCFZ0B1TQCAGF5tS3J98IwgRTit-H-fEVIao1LU,21045
numpy/lib/tests/test_io.py,sha256=6dKOB10hwTFb-QCVmM1dqXBEN8deTlNhPKWvVSIqJHk,112995
numpy/lib/tests/test_loadtxt.py,sha256=BXuhaSKd4AgH28nlR7KW9Bfd9hr_CMSagD8j2fQEGPI,41658
numpy/lib/tests/test_mixins.py,sha256=eWaFNkjo_IPlP4-7T-sitpZqhAgjUOjqkglUaPIdWXA,7224
numpy/lib/tests/test_nanfunctions.py,sha256=hWT_aag6WT21ODv_RP_2t25iUTd8rPuMoIS83vcDgWk,55536
numpy/lib/tests/test_packbits.py,sha256=RAk590EWlPvH9M3trkBKb6MbKNhMjo-otRLEYznT7dM,17919
numpy/lib/tests/test_polynomial.py,sha256=8SxedN9XiDR4wbntJR74M1KYgveOLKu4sxyYNq2kz0I,12632
numpy/lib/tests/test_recfunctions.py,sha256=PhSi_3CYmrLk6BaZYQh_4N54R_35g4_DdgTT269BOj8,44980
numpy/lib/tests/test_regression.py,sha256=6Us-PZWNVT__IW4vj2-rA77Sdhpaoqo-_JgySVXL3dg,7947
numpy/lib/tests/test_shape_base.py,sha256=R65ZPBfWnvzIyIrYbyJAUBUQqDbZX3gN0zx8sCydjKc,28219
numpy/lib/tests/test_stride_tricks.py,sha256=yUGgXm40x2pVYfxfmmtcVUFCPvfIlchxmx8rgx7-elU,23686
numpy/lib/tests/test_twodim_base.py,sha256=j9PmcG03wmHEZjOL6pMp-kKOczqjcYYYh8ptdFSBPnA,19484
numpy/lib/tests/test_type_check.py,sha256=UzaOYqNOWjSAxiur6FtERAEMIfYxk84nL_lpeDJGzxU,15269
numpy/lib/tests/test_ufunclike.py,sha256=7oc71qsMf8NSPU-bMOZNw7H5wwksjPlxy9jJHTMK9Bc,3112
numpy/lib/tests/test_utils.py,sha256=9XtDAa79N5LOqpLUHKY88ajRuY2gLzRMyJ1dry_4dkU,2454
numpy/lib/user_array.py,sha256=5z7-hfXnWT5Oq4_WPnjNWGPc9RHUWZcyq4L9ZM3kkGQ,64
numpy/lib/user_array.pyi,sha256=IaCNerLboKjt3Fm-_k_d8IqeyJf7Lc9Pr5ROUr6wleM,54
numpy/linalg/__init__.py,sha256=E24DtS109BNK_skVjGfSN491xPLM2nGarRjH-m1zBNM,2222
numpy/linalg/__init__.pyi,sha256=3M1fJPi1c3gyvXa0fIUmgwi_oDUi2Ps4d_9BKeeKNcw,1133
numpy/linalg/__pycache__/__init__.cpython-313.pyc,,
numpy/linalg/__pycache__/_linalg.cpython-313.pyc,,
numpy/linalg/__pycache__/linalg.cpython-313.pyc,,
numpy/linalg/_linalg.py,sha256=IBXkd-_T30mdDiz9k6VgIzMPKGsb6LpQiyjTmo_ap2M,118713
numpy/linalg/_linalg.pyi,sha256=5KaU6DM1fhPn8Ymjt49RtnpHaRjLTaAY-dZW7Etm5Oc,11623
numpy/linalg/_umath_linalg.cp313-win_amd64.lib,sha256=29d15Whc5Qb5R63IoclUmY5ymHJ8mFh2QfOqRz8AjR4,2120
numpy/linalg/_umath_linalg.cp313-win_amd64.pyd,sha256=IW6BydL-y6nbIbYRcND1wZRE_JDj7_V47K_Q2-qAsX8,111616
numpy/linalg/_umath_linalg.pyi,sha256=g5NJoNte6CwuMFDfd55O8OvJv4lOi539VKAB-Mrc864,1470
numpy/linalg/lapack_lite.cp313-win_amd64.lib,sha256=z5pvRdjISS003Whka5eSoek1E9cdE9-D8qhmjNiKk-k,2084
numpy/linalg/lapack_lite.cp313-win_amd64.pyd,sha256=XLrbdLj8ZidRC9WtZl_yJylnbG2JRyLTVZCXGDTp_ig,18432
numpy/linalg/lapack_lite.pyi,sha256=yQWhNIbR6YSzeGAumEJQTTkyBl2YRW_WlgsgGFDz0_E,2813
numpy/linalg/linalg.py,sha256=-uK-TpPOrpX8ItD4xpLi1kva6HZKfN-39-3h-yOAhOc,602
numpy/linalg/linalg.pyi,sha256=iGd8b4-gN1d92K7wfgDZxoHrVXnVC1c6vGqW4ZbWldY,1001
numpy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/linalg/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/linalg/tests/__pycache__/test_deprecations.cpython-313.pyc,,
numpy/linalg/tests/__pycache__/test_linalg.cpython-313.pyc,,
numpy/linalg/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/linalg/tests/test_deprecations.py,sha256=GaeE3JnQlJLoAfbY93LmgCFUlV5M8IFmQ7EhF4WbqwU,660
numpy/linalg/tests/test_linalg.py,sha256=km4njH7AS_jYw2rSmhKqWd1X5qPicCJ8p0a1j191XaE,86742
numpy/linalg/tests/test_regression.py,sha256=Z3RUgyBrcKAnvG1lAbluu7fsWDq7ApMKeCAEK2yaM2Q,6885
numpy/ma/API_CHANGES.txt,sha256=U39zA87aM_OIJhEKvHgL1RY1lhMJZc1Yj3DGLwbPbF0,3540
numpy/ma/LICENSE,sha256=1427IIuA2StNMz5BpLquUNEkRPRuUxmfp3Jqkd5uLac,1616
numpy/ma/README.rst,sha256=_MHrqHTE8L4wiJJqvaOh1l-xTxidwdilc_SZkFbgubM,10110
numpy/ma/__init__.py,sha256=Zh2Hil4sdNNkf-0aJQrnOPmRkRwR4rOAzhN-n3RHsbU,1459
numpy/ma/__init__.pyi,sha256=IorrWDELrFWTc_WfqWNCtWIcL0PrRE9aEZSzAABKMks,7404
numpy/ma/__pycache__/__init__.cpython-313.pyc,,
numpy/ma/__pycache__/core.cpython-313.pyc,,
numpy/ma/__pycache__/extras.cpython-313.pyc,,
numpy/ma/__pycache__/mrecords.cpython-313.pyc,,
numpy/ma/__pycache__/testutils.cpython-313.pyc,,
numpy/ma/core.py,sha256=Ag94aU0zq3R7ScJBiHv7ebpzQ8fpmrb63GS_cqgMt_E,297817
numpy/ma/core.pyi,sha256=fM6g8d76-3jUS6JoaT8IFSRAreC7Z2VcN1wZlPtMjoE,41921
numpy/ma/extras.py,sha256=MeTR8oKWzwLDkHKPPnx-8UNxVZcaR3CaAxFSP15Pa6Q,73024
numpy/ma/extras.pyi,sha256=2345YMvtTIztPgpuE_Xx8WfHYvQlTAyLzKvZ0foWJfs,3928
numpy/ma/mrecords.py,sha256=HVCPtj2VnbAI48PGeh_Ax3_J1Vt9ol1GVYS_f4FDeI0,27846
numpy/ma/mrecords.pyi,sha256=la8iCUB-Sw1diC0sBPgvludFTdYa2PT8DKcux04nrr4,2069
numpy/ma/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/ma/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_arrayobject.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_core.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_deprecations.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_extras.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_mrecords.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_old_ma.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_subclassing.cpython-313.pyc,,
numpy/ma/tests/test_arrayobject.py,sha256=ap06C0a0dGWcOknpctbhLbzHSNd2M9p_JL2jESqBBGk,1139
numpy/ma/tests/test_core.py,sha256=uDpzzooFh2bexYKu7GiHZX2eP1WdkNHriiFJ_YsNheY,225603
numpy/ma/tests/test_deprecations.py,sha256=3I1UN8R9CPORIisD_2A4wHfs1o_uraBbYZwEKgeMNmI,2656
numpy/ma/tests/test_extras.py,sha256=HMTxWuXupM2mLq7BoddDmDOh2cD6WziBMWQkt14Bejk,80433
numpy/ma/tests/test_mrecords.py,sha256=r0aiKGwuC7LOLaOuzXLQRz8s-b-c2l4sEWMP-Ih-a0g,20391
numpy/ma/tests/test_old_ma.py,sha256=2CJYQxU-vNkfxSC5AnsezbCuEnczf-9rvfhDbcDRqxY,33960
numpy/ma/tests/test_regression.py,sha256=d5LUwBxmENTnGAI1mb7D8wnv9b_PO7x4rxIxzTCLLGw,3403
numpy/ma/tests/test_subclassing.py,sha256=NtEEZJEPpDSsSU0Ahk59kETgUk6fVqMBP3ghdSjiUCo,17405
numpy/ma/testutils.py,sha256=WolXt_fMEpQl1pqfh9nWPCtD9A2Cpflu0dSIZetzf_c,10529
numpy/matlib.py,sha256=xGJk9kOBs7qqA8IqhqQuwufNMUvq6Af_mErXxmZHZxw,11018
numpy/matlib.pyi,sha256=JxPQ3A1oaYZW90E8Fkg3VaCp8kMJZTRmES7-mBq2ybs,10184
numpy/matrixlib/__init__.py,sha256=aPXbaN4OYDp9TFA8kGzt2gTBHb3o8Nanw-uM_3XoDF4,255
numpy/matrixlib/__init__.pyi,sha256=4sC3yICi3UozT9WZtjx3DqbZs7_vRQDUUy5HIraEe0w,111
numpy/matrixlib/__pycache__/__init__.cpython-313.pyc,,
numpy/matrixlib/__pycache__/defmatrix.cpython-313.pyc,,
numpy/matrixlib/defmatrix.py,sha256=HaFYtHEIhmi4KKbwzUJw-5uk-Xah8JS9dRVi2bZH20w,31994
numpy/matrixlib/defmatrix.pyi,sha256=hdoqtYixn7k4fJ4kFJBuyJ5VzVrFuTPj5qefm39_dlk,495
numpy/matrixlib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/matrixlib/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_defmatrix.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_interaction.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_masked_matrix.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_matrix_linalg.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_multiarray.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_numeric.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/matrixlib/tests/test_defmatrix.py,sha256=pXRWM5Ogw8TYguVybYH9sJHjFZwW3hY5I3Ye0WrlI_U,15432
numpy/matrixlib/tests/test_interaction.py,sha256=1nkRPWmfWfzOS9GjwOQV1D-Ke9W6UtkdwuMMVYcRIcg,12234
numpy/matrixlib/tests/test_masked_matrix.py,sha256=nopfUUI8kKuKRiV9G0rQhRdc6K6I6mZ8KtN9NVWzawI,9027
numpy/matrixlib/tests/test_matrix_linalg.py,sha256=oW_ejZQoARPqzYtnrXDOVkSRcMT0JJJa9vcOmrr_jw8,2254
numpy/matrixlib/tests/test_multiarray.py,sha256=WgUxpIxXbzpXTTnt8iG0HC6RoAcTUk0PsEiMHT2361E,572
numpy/matrixlib/tests/test_numeric.py,sha256=4XC1O2mv7NYYP1siT6I0YAz-Cuhlw6ZN9Du2FmCUz8Y,465
numpy/matrixlib/tests/test_regression.py,sha256=2-c4B5aKWbySZ95RlG5WUm8OLBO7sLtM2zIzQMwMv0U,965
numpy/polynomial/__init__.py,sha256=ynOHE1Mc9eBZMNlroaE9meIW7wZkvo7bpGjFjtDB_AU,6913
numpy/polynomial/__init__.pyi,sha256=jc9FycOQGDkZNxh4Xb_pavPuPVsSJTIa-HLzk-aNVWM,713
numpy/polynomial/__pycache__/__init__.cpython-313.pyc,,
numpy/polynomial/__pycache__/_polybase.cpython-313.pyc,,
numpy/polynomial/__pycache__/chebyshev.cpython-313.pyc,,
numpy/polynomial/__pycache__/hermite.cpython-313.pyc,,
numpy/polynomial/__pycache__/hermite_e.cpython-313.pyc,,
numpy/polynomial/__pycache__/laguerre.cpython-313.pyc,,
numpy/polynomial/__pycache__/legendre.cpython-313.pyc,,
numpy/polynomial/__pycache__/polynomial.cpython-313.pyc,,
numpy/polynomial/__pycache__/polyutils.cpython-313.pyc,,
numpy/polynomial/_polybase.py,sha256=8YgTcSVA4nRyITvWfrvuFGkXAdRB8Cc01R3cMCaa2wI,40549
numpy/polynomial/_polybase.pyi,sha256=C5j9h0s698rgbcDMKGWWrwrF5QYtnwa6Ox_244WP9mg,8472
numpy/polynomial/_polytypes.pyi,sha256=MRY_GKoGtkKCFYvVt_mh94KOIpPhni4M4VkuB54y7Bo,23274
numpy/polynomial/chebyshev.py,sha256=zxrQ6wuu3IZ_VYy4BpAhkKkCrymvdQuIRcc6MUSNYHg,64325
numpy/polynomial/chebyshev.pyi,sha256=LhCLFybWm6XsSqPJblYoaBWkG1IcTf0bFtUuR0rd4kY,4968
numpy/polynomial/hermite.py,sha256=EhhX8RbRtTl42tynAFhgbImeGJO1M2gMjE0hRphjKfk,56343
numpy/polynomial/hermite.pyi,sha256=dCO5Pv_LMeFW-nwKfRhN7kTATaXZSPgmAbtLo7TX6-I,2570
numpy/polynomial/hermite_e.py,sha256=WJkoibBSLSeR0RrE3dL64hUTUhsdgQ2QGGF7hqUCVEU,53947
numpy/polynomial/hermite_e.pyi,sha256=vXlYcf-jHnS0V6T8gbDtgdyPJknfHR5iP4klDDdAvlg,2662
numpy/polynomial/laguerre.py,sha256=WnoywsFjgqsmZN6NaGCwX2cOPKANoPDvvEs5bDAndHs,54149
numpy/polynomial/laguerre.pyi,sha256=3Aj5K9_lvjn4HMBEMvRj1NUjAybxEV0odA7u-LFAKRA,2291
numpy/polynomial/legendre.py,sha256=kfEY3rDouSItAPtI1_sdY4vQXtVKAkOOdEgEYttcBSM,52734
numpy/polynomial/legendre.pyi,sha256=S2bUMiQuuzKbwkryAjTrkmJQtgyFNGbw3Z5RZ8dEymY,2291
numpy/polynomial/polynomial.py,sha256=uMkcK4AmTjT3NQqB_tI7sTTGUZMrWzrfHNWKYhTIQcw,53812
numpy/polynomial/polynomial.pyi,sha256=li6W0OTUkSnmlHmbC7eiTZXVBTUYtHsNuQhkw4ZTi7o,2110
numpy/polynomial/polyutils.py,sha256=WIkpxJavMmDBnb1RqEXZqtQkyuBoyTKqV-ljaC9L0Fc,23416
numpy/polynomial/polyutils.pyi,sha256=-2XO3ggSvxc8VmcxgK0k73Z10E0nRn1fD8bnzGnIuA8,10658
numpy/polynomial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/polynomial/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_chebyshev.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_classes.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite_e.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_laguerre.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_legendre.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_polynomial.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_polyutils.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_printing.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_symbol.cpython-313.pyc,,
numpy/polynomial/tests/test_chebyshev.py,sha256=ABZkUVV2ju2Qz6ZcunGSUp4IixYRkXqlQKdpcrluUVs,21273
numpy/polynomial/tests/test_classes.py,sha256=vRYXxgoDP0w2ucVRV0JGjA_iqtN1rkPa9YaxtSXJNa8,19170
numpy/polynomial/tests/test_hermite.py,sha256=OlAQnE-sI0WVVixyBaZj8dhO-dOx_KJqBqLLPI8l69E,19245
numpy/polynomial/tests/test_hermite_e.py,sha256=hqmpyykKvJXo7YG1uQ6_NG_LxaDMH_LFUGr5BBO3DjA,19585
numpy/polynomial/tests/test_laguerre.py,sha256=T-801KTxjscADvIjYhjyM0VTfKYBZS7QalenxQIzTj0,18177
numpy/polynomial/tests/test_legendre.py,sha256=kffyVxgi0D6zcTlZfmhf1AY3lQ-9wgyTaKOZvfClUaA,19376
numpy/polynomial/tests/test_polynomial.py,sha256=8fgVtyZ-E8HH0E7YxhZcvtyE5AVSM5BcUgt8UrccdaE,23580
numpy/polynomial/tests/test_polyutils.py,sha256=1u8P9Ta0FsV4y2pMRjIgXf_38oMxe8xIJplJ8AFw0n0,3908
numpy/polynomial/tests/test_printing.py,sha256=xkSxkYUUaAdBjayCrzRSca7p4aG7kyIqPRiy1b2FxEk,21958
numpy/polynomial/tests/test_symbol.py,sha256=JNF0yt5xmUERjflLOydG1pMo6wMRLtaPDcYUkvXcD-o,5592
numpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/LICENSE.md,sha256=tLwvT6HJV3jx7T3Y8UcGvs45lHW5ePnzS1081yUhtIo,3582
numpy/random/__init__.pxd,sha256=g3EaMi3yfmnqT-KEWj0cp6SWIxVN9ChFjEYXGOfOifE,445
numpy/random/__init__.py,sha256=8h45GRbXpL10xJzqw_n6Xgnm6SY_JUYCqH6SpeCnqUY,7693
numpy/random/__init__.pyi,sha256=aqBCk_fpEZeoE94eNpGu37F6ZWfaDaajac9cWNm97So,2233
numpy/random/__pycache__/__init__.cpython-313.pyc,,
numpy/random/__pycache__/_pickle.cpython-313.pyc,,
numpy/random/_bounded_integers.cp313-win_amd64.lib,sha256=hLA8Zvd0ohOrB3sH8Dx14a33FMG4TZR8tWYohjo-yH4,18000
numpy/random/_bounded_integers.cp313-win_amd64.pyd,sha256=NIpvpJylkAOr1PemEx3CFXcL3asWzHTNHvCsxLOozlA,211456
numpy/random/_bounded_integers.pxd,sha256=EOKKUlF9bh0CLNEP8TzXzX4w_xV5kivr1Putfdf6yvU,1763
numpy/random/_bounded_integers.pyi,sha256=PFr_V0xYQhWjKk5oc83cYg_JcNZ2FEKTsjXlnxmkyB8,25
numpy/random/_common.cp313-win_amd64.lib,sha256=4wotLUuXHEyV6X7LOpVWpyO__oER0_RGTy7Ac5QIriE,2012
numpy/random/_common.cp313-win_amd64.pyd,sha256=li1XaPhTRhNVa6wV6E4c2WkDx2w40s7G8nMrvxCHK0Q,164864
numpy/random/_common.pxd,sha256=2_9NLWFSnLG4iDd-KeYUBRa47QM8qceUsPiAkyWZ74I,5089
numpy/random/_common.pyi,sha256=UlOkH40kVn6TU0c6OhG3CocvGnuYAC_46fxZJ7B_7y8,437
numpy/random/_examples/cffi/__pycache__/extending.cpython-313.pyc,,
numpy/random/_examples/cffi/__pycache__/parse.cpython-313.pyc,,
numpy/random/_examples/cffi/extending.py,sha256=jSc3Vc6Uxl3VWHmoaffez8qG0GTfrFMuUxDhuB9Y5z4,928
numpy/random/_examples/cffi/parse.py,sha256=2hy5736s-oL5uYvlQf_acpo7srBC8WfffLUhMcm218c,1803
numpy/random/_examples/cython/extending.pyx,sha256=1lkq6zFifnwaMtAkVG0i_9SbMiNqplvqnHaqUpxqNzs,2344
numpy/random/_examples/cython/extending_distributions.pyx,sha256=bGYjid42hpSsxDrpw76gYZl7fVbcE3U3WkDzb2aLbAE,3984
numpy/random/_examples/cython/meson.build,sha256=q_IFcVs_qzERJD_-8uaDnjps3QdaW49okZMbFtwkAPo,1747
numpy/random/_examples/numba/__pycache__/extending.cpython-313.pyc,,
numpy/random/_examples/numba/__pycache__/extending_distributions.cpython-313.pyc,,
numpy/random/_examples/numba/extending.py,sha256=mo0o4VM-K1vUQxNl_Uqr35Acj9UewnkglS7-dFX8yuw,2045
numpy/random/_examples/numba/extending_distributions.py,sha256=vQdhhOpuGlpG8hk-mKWv7Li3-rwvelv-1c67odurt9o,2103
numpy/random/_generator.cp313-win_amd64.lib,sha256=IBI5Jymh7ePH2t8Jr_Oa3DUP0GS2upumzNzcaxBNf0A,18400
numpy/random/_generator.cp313-win_amd64.pyd,sha256=kUo7iyHeSSrHKOuMcuHwqBit_p8ksJwA1pcNmuB9Cfg,719360
numpy/random/_generator.pyi,sha256=kCKrx-vcBtZ4sSrIOurp9wz3gLcrF-4daoxDxfE4It0,24865
numpy/random/_mt19937.cp313-win_amd64.lib,sha256=QVVSD_ux5YdTwkWii_dEiE5g_QDqWmhuKosWOKExMCM,2032
numpy/random/_mt19937.cp313-win_amd64.pyd,sha256=ki3272RxBDyuGC0qF2y9FHRJ2HyNjeQEJHfSuWj2v5c,84480
numpy/random/_mt19937.pyi,sha256=qL913uLEvulCbydu94TsioiIWFnkCRmCqvDu_JHmkJs,800
numpy/random/_pcg64.cp313-win_amd64.lib,sha256=eP6a2upQrFwSLA6tHfUntFv7RRNFAAHXj7lCucwowuM,1996
numpy/random/_pcg64.cp313-win_amd64.pyd,sha256=HbBNN4JpqEZwvrogrW2nZ_pyjSpXHweM51tXZpN2E6c,95232
numpy/random/_pcg64.pyi,sha256=grAVLs9HjNKi-4RFjMqp_46ipAJ2mbhimFsUtikwANI,1186
numpy/random/_philox.cp313-win_amd64.lib,sha256=mxwJ2cwic_fXNRQueX_zbbioU5o6gbkfQZa804nQe0g,2012
numpy/random/_philox.cp313-win_amd64.pyd,sha256=_I8WGsVfmrgNTSz6Xab41s8uhw_jjBOpgtuCqn1vG8A,78848
numpy/random/_philox.pyi,sha256=h_tI9myU0GPYynMIUXh7xj_fqLxtKdDq6X0TxCnH-ZI,1044
numpy/random/_pickle.py,sha256=8fmUcgzHhq_F_eyesNUdFjV07Br1yzLBLsfe-GWyQrE,2830
numpy/random/_pickle.pyi,sha256=hj1oBasr_ejSeUlT3-q7luwC4DQFqgCPXIL0wxuFjt4,1651
numpy/random/_sfc64.cp313-win_amd64.lib,sha256=uayHZwPeopMvwea1JIbSnojqnl5e956VRWUpQyvDc1Y,1996
numpy/random/_sfc64.cp313-win_amd64.pyd,sha256=BQQNO2FlNlqUcobjAcwv-1V9XkCQPT2e_lmqAR3NeYU,58880
numpy/random/_sfc64.pyi,sha256=kvQdsz_a2EUH5Tl8v1WjJacDJb7Jk_MWw7-dp7q3kXo,710
numpy/random/bit_generator.cp313-win_amd64.lib,sha256=mShyvXDmDBK5GFvWr5xkquNQ9jD4vSi1bzfrF8_IEqc,2120
numpy/random/bit_generator.cp313-win_amd64.pyd,sha256=vYVtwi-khSn7DI7IV9xlYswrAnC8nLrncSr5XYD6Huw,166400
numpy/random/bit_generator.pxd,sha256=LJpeB-EKeVV8_JO69sS33XJLZQ3DAhrUCNzs_ei7AoI,1042
numpy/random/bit_generator.pyi,sha256=k1MV8-6mf6O7SOlwXYkC5KETtduEZgdzsZkQOB7Yg6k,3728
numpy/random/c_distributions.pxd,sha256=02WeqbzQ4heQ1cZ7ShePejxmt5AOI5kTstBZ5w2WxD0,6454
numpy/random/lib/npyrandom.lib,sha256=rAfhAJqvaa622YQ2ah0pkjMge1nl1HbUCmZXdQ_JDrY,150380
numpy/random/mtrand.cp313-win_amd64.lib,sha256=t91KjEHyk14UcbEdedgLC0k-F_AHnEJMJoFjpnkt6W0,17122
numpy/random/mtrand.cp313-win_amd64.pyd,sha256=uKG9xYIBVnA6kSFoFNiAicbCu90IPCrPRaVeQAVGdfU,605696
numpy/random/mtrand.pyi,sha256=qMZhn2QPpftnyYxSeqN_umFbDtcVwh2tMNxwlfUNm-I,23390
numpy/random/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_direct.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_extending.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937_regressions.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_random.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_randomstate.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_randomstate_regression.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_seed_sequence.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_smoke.cpython-313.pyc,,
numpy/random/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/__pycache__/__init__.cpython-313.pyc,,
numpy/random/tests/data/generator_pcg64_np121.pkl.gz,sha256=EfQ-X70KkHgBAFX2pIPcCUl4MNP1ZNROaXOU75vdiqM,203
numpy/random/tests/data/generator_pcg64_np126.pkl.gz,sha256=fN8deNVxX-HELA1eIZ32kdtYvc4hwKya6wv00GJeH0Y,208
numpy/random/tests/data/mt19937-testset-1.csv,sha256=bA5uuOXgLpkAwJjfV8oUePg3-eyaH4-gKe8AMcl2Xn0,16845
numpy/random/tests/data/mt19937-testset-2.csv,sha256=SnOL1nyRbblYlC254PBUSc37NguV5xN-0W_B32IxDGE,16826
numpy/random/tests/data/pcg64-testset-1.csv,sha256=wHoS7fIR3hMEdta7MtJ8EpIWX-Bw1yfSaVxiC15vxVs,24840
numpy/random/tests/data/pcg64-testset-2.csv,sha256=6vlnVuW_4i6LEsVn6b40HjcBWWjoX5lboSCBDpDrzFs,24846
numpy/random/tests/data/pcg64dxsm-testset-1.csv,sha256=Fhha5-jrCmRk__rsvx6CbDFZ7EPc8BOPDTh-myZLkhM,24834
numpy/random/tests/data/pcg64dxsm-testset-2.csv,sha256=mNYzkCh0NMt1VvTrN08BbkpAbfkFxztNcsofgeW_0ns,24840
numpy/random/tests/data/philox-testset-1.csv,sha256=QvpTynWHQjqTz3P2MPvtMLdg2VnM6TGTpXgp-_LeJ5g,24853
numpy/random/tests/data/philox-testset-2.csv,sha256=-BNO1OCYtDIjnN5Q-AsQezBCGmVJUIs3qAMyj8SNtsA,24839
numpy/random/tests/data/sfc64-testset-1.csv,sha256=sgkemW0lbKJ2wh1sBj6CfmXwFYTqfAk152P0r8emO38,24841
numpy/random/tests/data/sfc64-testset-2.csv,sha256=mkp21SG8eCqsfNyQZdmiV41-xKcsV8eutT7rVnVEG50,24834
numpy/random/tests/data/sfc64_np126.pkl.gz,sha256=MVa1ylFy7DUPgUBK-oIeKSdVl4UYEiN3AZ7G3sdzzaw,290
numpy/random/tests/test_direct.py,sha256=CgrDVzkR5Lcoi074uGyPKGFLYTIZb0WyH2maZFXmiAI,20511
numpy/random/tests/test_extending.py,sha256=8Q5gcLChpe1RO5wVRSbBbMnzQ7Z93nNrqHZ0Y9_wnV4,4659
numpy/random/tests/test_generator_mt19937.py,sha256=UIo1NUwkchIn1LiDB0aqoAx-K4zuRUsz_aAd0Oyanj4,120616
numpy/random/tests/test_generator_mt19937_regressions.py,sha256=UiPzhq1MGFp8EflTCSMErrqp70tzy8SETGx3cLNCC-I,8314
numpy/random/tests/test_random.py,sha256=yJQ8UBPQhTj0fawv_yO_CASrhsDEXt1umxZZyVp-4XI,72055
numpy/random/tests/test_randomstate.py,sha256=4FxsJI2EkXYdZIoQWGZNbzHEBw-5Y3FEP7w0eYUMV-c,87879
numpy/random/tests/test_randomstate_regression.py,sha256=YRHZRSDlaJ2GJWwB8busbD63jElcSg-7jQi3ON3EIxM,8227
numpy/random/tests/test_regression.py,sha256=uNv4WtIhzyTHm88FoVdi6pLUyAJC9OsD1FYF6KwSuCI,5623
numpy/random/tests/test_seed_sequence.py,sha256=J_peqBY4MhduYR1fFYAfcN2wvyC7P6xmV4xbu6j9nbQ,3389
numpy/random/tests/test_smoke.py,sha256=oRHbPUDkj1l9V6TVP0SNtLktBxdJR3dK2Ek_KpZc9yk,28960
numpy/rec/__init__.py,sha256=cgaZYq6w4qNo81NZGO-E4vkSj9eSO4SgNMOqiLglp4k,85
numpy/rec/__init__.pyi,sha256=gGrssJCiTrltTcwaCjXB8saZBWiWCHOr2mJmUsFdU50,370
numpy/rec/__pycache__/__init__.cpython-313.pyc,,
numpy/strings/__init__.py,sha256=JzKUIYVjG4wRzsKVAVg9XWrq2vjjdi679CZc_Txfn4w,85
numpy/strings/__init__.pyi,sha256=LvbeB_oUcN7O0GUJx3gzfYs-KPPVhHm6EhJIG3hJAHQ,1416
numpy/strings/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/__init__.py,sha256=0Qkz0ITfPKHDe9kObKTEx3fdDGAb9tfMikpdPuiapyA,603
numpy/testing/__init__.pyi,sha256=hzSq3lVZ2gZbxMrQXNP3PaetjgJyKnfg50mkjTB8jXg,2147
numpy/testing/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/__pycache__/overrides.cpython-313.pyc,,
numpy/testing/__pycache__/print_coercion_tables.cpython-313.pyc,,
numpy/testing/_private/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/_private/__pycache__/extbuild.cpython-313.pyc,,
numpy/testing/_private/__pycache__/utils.cpython-313.pyc,,
numpy/testing/_private/extbuild.py,sha256=ausYJDf2eOq9jYC3x_tFO_vBrMhjqjruVqHxgHjla8w,7966
numpy/testing/_private/extbuild.pyi,sha256=FWRL9bv2CK1FpFNLGXEJLvoZN6jgdQNnb62EENQ_u6Y,651
numpy/testing/_private/utils.py,sha256=wSeRnSpJvxf8Pu687KJTeWJTqobo-Z02tp0aMDd9mkk,98146
numpy/testing/_private/utils.pyi,sha256=pNPvwPkm3QbPdJ_Z98uvCmYZYmJS8abmBXjCIIz3Tgg,13442
numpy/testing/overrides.py,sha256=rldmRQXc5c9jEs4hghDXvHA4sJD7HuMcpfGMmzSML9I,2218
numpy/testing/overrides.pyi,sha256=LMYa6hii8jPmR_eC-LHNrz3irrImvZcW29NxCkfgzNk,408
numpy/testing/print_coercion_tables.py,sha256=lT8IdI1_lantwFVG0C0JagO0mUxnnXCA5wnPI81czYQ,6493
numpy/testing/print_coercion_tables.pyi,sha256=z88CLUr9I9HOQ0LOpgDjK-hXPuVKnQNMi26Oz99TNBk,848
numpy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/tests/__pycache__/test_utils.cpython-313.pyc,,
numpy/testing/tests/test_utils.py,sha256=d49srmN7Jxu4jmCK9e48R0ZfIxNEg38Op4H7To2GJQg,71492
numpy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/tests/__pycache__/test__all__.cpython-313.pyc,,
numpy/tests/__pycache__/test_configtool.cpython-313.pyc,,
numpy/tests/__pycache__/test_ctypeslib.cpython-313.pyc,,
numpy/tests/__pycache__/test_lazyloading.cpython-313.pyc,,
numpy/tests/__pycache__/test_matlib.cpython-313.pyc,,
numpy/tests/__pycache__/test_numpy_config.cpython-313.pyc,,
numpy/tests/__pycache__/test_numpy_version.cpython-313.pyc,,
numpy/tests/__pycache__/test_public_api.cpython-313.pyc,,
numpy/tests/__pycache__/test_reloading.cpython-313.pyc,,
numpy/tests/__pycache__/test_scripts.cpython-313.pyc,,
numpy/tests/__pycache__/test_warnings.cpython-313.pyc,,
numpy/tests/test__all__.py,sha256=xZkp3RbMNpx4bFTvILKV8KTEMh5lvId7xcrhQS4LTe0,232
numpy/tests/test_configtool.py,sha256=Dzn7GlIiV-yRjOExZ6V1M9UpY0bRb0n7gRtusWlEEoA,1787
numpy/tests/test_ctypeslib.py,sha256=Tiil9BYqkbUQ2-UuMSD1DscdRlAF4ObaKfXklitozvk,12752
numpy/tests/test_lazyloading.py,sha256=1_nY3uWUmVjTFWPh6qPifLEuctERoJ7UuuJJmsOZRj8,1198
numpy/tests/test_matlib.py,sha256=KmBMo3M7IARB8K5NLYk611RtsfW10_LgCQEBjdLEM9g,1913
numpy/tests/test_numpy_config.py,sha256=wbgbqmiKjSW4ZiPGlGs634J8x8wcC5Fjp-YLEVyQp6I,1281
numpy/tests/test_numpy_version.py,sha256=EhDAFEamNCmRAiJEUSGtPa21IipODWrf6MN2Bem0az8,1798
numpy/tests/test_public_api.py,sha256=It0dkHfJTwE-vClq6zJXfbIcC9ccxJVbgfrXPyu6mdU,28657
numpy/tests/test_reloading.py,sha256=8F8bSKvw6sAWp4Acaxl_16FkE1_IYGySdotdjmb-_j0,2441
numpy/tests/test_scripts.py,sha256=yy6LRo-45AZ9y5GnTf7oNTgghXr9jV2a_zkc8hpW0wU,1714
numpy/tests/test_warnings.py,sha256=qRTUhQtsxR6U9eqwozRmxL3ZPtWzh7ZzdCBvW8sXA7k,2406
numpy/typing/__init__.py,sha256=Kf63cjIRpICOlKiC178BM7yUXnDP33c_b8tdzncxe-g,6249
numpy/typing/__pycache__/__init__.cpython-313.pyc,,
numpy/typing/__pycache__/mypy_plugin.cpython-313.pyc,,
numpy/typing/mypy_plugin.py,sha256=EofOXhajhpwKDLxRRbboAN3u2cnIARkQvGXWnzw6Gm8,6736
numpy/typing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/typing/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/typing/tests/__pycache__/test_isfile.cpython-313.pyc,,
numpy/typing/tests/__pycache__/test_runtime.cpython-313.pyc,,
numpy/typing/tests/__pycache__/test_typing.cpython-313.pyc,,
numpy/typing/tests/data/fail/arithmetic.pyi,sha256=tYgbkEhkdA2YoIj7STBRTFDlKnKwuJ2IQAEWYjMM7ck,3821
numpy/typing/tests/data/fail/array_constructors.pyi,sha256=bNPDfCMnoFPuMi4bGDEf4kQ5wqbrAB7PkfDqFCfpIBc,1234
numpy/typing/tests/data/fail/array_like.pyi,sha256=9EcZ306eJOZrHSdWlTe0cEpYvmODaQ_nItcbhWaxFy8,511
numpy/typing/tests/data/fail/array_pad.pyi,sha256=ExxQs_3s8xvgEsvj6O4aGWTIc0ukXtAtUM9JUs5D6pQ,143
numpy/typing/tests/data/fail/arrayprint.pyi,sha256=LXaf_eDZOAHoBUtegnMDiHxaehcN0xLQh_AcvVBlfho,632
numpy/typing/tests/data/fail/arrayterator.pyi,sha256=L2AC5qFmBrw9klehdV7d7gHvKpPX-7H1Iz9wryDQVQA,477
numpy/typing/tests/data/fail/bitwise_ops.pyi,sha256=5c5tolQB7Jaixhy0E4XuE_p8VXgE7zOQwKDppIb8KBQ,421
numpy/typing/tests/data/fail/char.pyi,sha256=IXHqCM6aqVyfX74jg5lTei1NciRFQSeWNYcholumPzA,2865
numpy/typing/tests/data/fail/chararray.pyi,sha256=bHPeaIeMG7QMAPvZx7oh0wNe0ZohjjKE0UuAMzAEFho,2418
numpy/typing/tests/data/fail/comparisons.pyi,sha256=YHRMGwDA3uh5DJdLtG0aJ8Eo5Rs3DofVZNMI2KxNWTg,777
numpy/typing/tests/data/fail/constants.pyi,sha256=MnjvGyU_QKKiSZ-BzsHdojlRKz7GGvqQmu0mxbewcao,81
numpy/typing/tests/data/fail/datasource.pyi,sha256=PUUqHW6izqDMQnWoQ5oDYW8oeyijpqTM85G3S5qpElM,434
numpy/typing/tests/data/fail/dtype.pyi,sha256=TeNsugh9LvvX-u08MkROtFM_B080JZheXexyw9Wsq6E,322
numpy/typing/tests/data/fail/einsumfunc.pyi,sha256=DeLM2jL7ZBirjw9H2dfjXxbHwx2Y9gu3wp2NwDLYuIQ,470
numpy/typing/tests/data/fail/flatiter.pyi,sha256=vyjvoG2le_3IePcYE1qBbnzJLoaGB7BQuF3Nu8PueME,735
numpy/typing/tests/data/fail/fromnumeric.pyi,sha256=b8GZaCTjij23mNabdvETIEriMLtDUZb3k1oD7S_Nz-Y,5833
numpy/typing/tests/data/fail/histograms.pyi,sha256=vkSk1v1Na4VUnRlwM8sqhxqPjhg2HH_9YtXnYNEHgPM,388
numpy/typing/tests/data/fail/index_tricks.pyi,sha256=2VDHr1Of7fqh2uu0wf-epw7VZD2Fy0-W9ZPe1Fa87-8,531
numpy/typing/tests/data/fail/lib_function_base.pyi,sha256=HuJ3WQheNnE7iikA179-usGH8zT8YglirsxSKVoIqGc,2879
numpy/typing/tests/data/fail/lib_polynomial.pyi,sha256=xjpuJ7DVIRxQok6f0-CGsSVg6QQuzZmyiURslKF3ctw,966
numpy/typing/tests/data/fail/lib_utils.pyi,sha256=iBUetgF7F39F-yV2DBhIqvKywzK8kZoAL9N-kpR3pyk,101
numpy/typing/tests/data/fail/lib_version.pyi,sha256=EfAZTQpzTJ1UCY3p9envuaJUCqforENP_QP_DVWU7Do,160
numpy/typing/tests/data/fail/linalg.pyi,sha256=EDPHdTxXUPmerfi4BfT0dh6u-wbFqfEHFiHOuYFZIfs,1429
numpy/typing/tests/data/fail/ma.pyi,sha256=sfN1avmqVQl498LZdBc6Qb6fNA1IrobmgSZQJLKcHFA,6507
numpy/typing/tests/data/fail/memmap.pyi,sha256=U8_bCFw8m8x7ZlWSpYmmKpC1BS8oCEwUEdUgCRf2FSg,174
numpy/typing/tests/data/fail/modules.pyi,sha256=f14qw9HXlwJ7FARKYxNhaPou8OZ8mQXsU4P9cpFxbWI,620
numpy/typing/tests/data/fail/multiarray.pyi,sha256=xgnZSrX-2xi0UtRKljKmPfiIQ2w1fbYj5gicey04l80,1708
numpy/typing/tests/data/fail/ndarray.pyi,sha256=8wZpNNatpxbxNu8G2N_R0P-3UVZLVE_z6ZGmdndSWPM,392
numpy/typing/tests/data/fail/ndarray_misc.pyi,sha256=8EqdfwcoQMF980xhwdE6Lwr5n8rCGILUL8rkcTyi_5M,1097
numpy/typing/tests/data/fail/nditer.pyi,sha256=ZzT6O4P7IkrQA8MWDNYzO_r88NQ9RCJ1rHHdyaLR8zE,327
numpy/typing/tests/data/fail/nested_sequence.pyi,sha256=dmmC3YFiUAI2McpPRb4MmGGpdNOjCAbF4xntnt-qyds,479
numpy/typing/tests/data/fail/npyio.pyi,sha256=IvqhHyWTQdx7UTiCovji1oTer63nTMT5k8rMjoR3aR4,670
numpy/typing/tests/data/fail/numerictypes.pyi,sha256=igYnLB91EuhrNDihL9IxwlM_xhwphCgMjWimzDaSNrk,129
numpy/typing/tests/data/fail/random.pyi,sha256=ng4fxmdk1RFQdn3PNF7qSO2c2hVxQ07lwYwQGrSPsKQ,2965
numpy/typing/tests/data/fail/rec.pyi,sha256=HkJQLYK6u8thA6alMB4pUeMBPXr6mgnqqTYmXSiM2PM,758
numpy/typing/tests/data/fail/scalars.pyi,sha256=xlbuLZMeNbCRtxTD2L5RAzdMveduhwBKqVclIEwzgfY,2936
numpy/typing/tests/data/fail/shape.pyi,sha256=8oJrtWxbzpu4sXygzEMzWHlwa13SZHikfXH7T-feQkM,137
numpy/typing/tests/data/fail/shape_base.pyi,sha256=jfOTNjSqDVIGlAmwbORYfifftjvW-W4ngMRP883oSrU,165
numpy/typing/tests/data/fail/stride_tricks.pyi,sha256=kjsv-sHn8i-oxtLUcpeIrOzS00Hk6Af8clqnY-vwg6A,339
numpy/typing/tests/data/fail/strings.pyi,sha256=T7nJ-2UqEa2ld_bZW4king8PnAG9w9LompbD9rtIb7Y,2385
numpy/typing/tests/data/fail/testing.pyi,sha256=zBTsTLi6xASHSeRK4f9ub1810m33xrb2qFyGvH93iqo,1427
numpy/typing/tests/data/fail/twodim_base.pyi,sha256=L4Qi32BjiexVALZnjR5AY3uB6QJP_k_VRdLetIW_x6E,968
numpy/typing/tests/data/fail/type_check.pyi,sha256=_p6l-0V0LSN24C6V0hv2JtXNzTG1Lyroe-5KBa5IvG0,410
numpy/typing/tests/data/fail/ufunc_config.pyi,sha256=G17kqlgWREHukftCiDnCBRDjum5H1f8UIIifakzNBZc,610
numpy/typing/tests/data/fail/ufunclike.pyi,sha256=7dpF86m9EeY_69dl_zX0joQvFIpKPXGQRRUJOipWTOw,670
numpy/typing/tests/data/fail/ufuncs.pyi,sha256=7fKHGG69SFRRkqO5I-QqV-pVkQ3LTCPWgwgzOHGmhwI,522
numpy/typing/tests/data/fail/warnings_and_errors.pyi,sha256=kKR53mZ3zy1-JmyGgmWjBmJw04ipqnTzqu9B4nU5OlQ,205
numpy/typing/tests/data/misc/extended_precision.pyi,sha256=-64Tr47-NYe7JX33otKGsloq7msS7hiVg9ehAb_g8yo,331
numpy/typing/tests/data/mypy.ini,sha256=NwLOGZQO6x7GdH-YVXJdQjv07w-HC175VGkM978k580,254
numpy/typing/tests/data/pass/__pycache__/arithmetic.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_constructors.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_like.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayprint.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayterator.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/bitwise_ops.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/comparisons.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/dtype.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/einsumfunc.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/flatiter.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/fromnumeric.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/index_tricks.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_user_array.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_utils.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_version.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/literal.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ma.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/mod.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/modules.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/multiarray.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_conversion.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_misc.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_shape_manipulation.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/nditer.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/numeric.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/numerictypes.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/random.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/recfunctions.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/scalars.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/shape.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple_py3.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunc_config.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunclike.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufuncs.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/warnings_and_errors.cpython-313.pyc,,
numpy/typing/tests/data/pass/arithmetic.py,sha256=FW2UwWYNcMkSMztc66czAtR1dN95Tey0W4N-M4dIsm4,8374
numpy/typing/tests/data/pass/array_constructors.py,sha256=MGzgCt7uTeC_b7wU2aPlvTuDzXfgOujx_lR0Vqfpny8,2584
numpy/typing/tests/data/pass/array_like.py,sha256=0Cg3SZ_RjDsGCGiuIhyDeow3_kOOW5EHjPjLs1VvnBs,1075
numpy/typing/tests/data/pass/arrayprint.py,sha256=NTw1gJ9v3TDVwRov4zsg_27rI-ndKuG4mDidBWEKVyc,803
numpy/typing/tests/data/pass/arrayterator.py,sha256=z4o0H08T7tbzzMWhu5ZXdVqbivjBicuFgRHBk_lpOck,420
numpy/typing/tests/data/pass/bitwise_ops.py,sha256=8lfjgayfTDDcWi1O-rnxLu4FZqvskvGHvFXJpMQWQgc,1095
numpy/typing/tests/data/pass/comparisons.py,sha256=-NSAhFNN3kWqu2CZqt2pq3kflTx6nDCWxkO3JIYl5NI,3613
numpy/typing/tests/data/pass/dtype.py,sha256=YRsTwKEQ5iJtdKCEQIybU_nL8z8Wq9hU-BZmEO7HjQE,1127
numpy/typing/tests/data/pass/einsumfunc.py,sha256=CXdLvQsU2iDqQc7d2TRRCSwguQzJ0SJDFn23SDeOOuY,1406
numpy/typing/tests/data/pass/flatiter.py,sha256=_JYyZbW-Qs-YoOUzCRe1wFKErPNNJoNYBrdQXbNP3N8,222
numpy/typing/tests/data/pass/fromnumeric.py,sha256=bP0hEQYYQJOn7-ce0rAf8cvuxZX3Ja6GSSlCtNhEBUM,4263
numpy/typing/tests/data/pass/index_tricks.py,sha256=RyuEtqyZVlK9j403DVjMZFd80mvt-VAMi1uGvXurc0c,1462
numpy/typing/tests/data/pass/lib_user_array.py,sha256=K69fg9dI5BaglzpiJh13swGHuyx3LBW_zmzBBOB1aWw,612
numpy/typing/tests/data/pass/lib_utils.py,sha256=XEc0v7bwES-C5D4GkSJQSSTSAl5ng7tq6tCWj3jxbCM,336
numpy/typing/tests/data/pass/lib_version.py,sha256=TlLZK8sekCMm__WWo22FZfZc40zpczENf6y_TNjBpCw,317
numpy/typing/tests/data/pass/literal.py,sha256=U0Ja399uHwALtjW0RcSvt7r1ACJJSsMHM4h_-6Gkvyw,1559
numpy/typing/tests/data/pass/ma.py,sha256=ZxUSC614aOd76UM64Gkffrc-TPZAukjuo0b5NCxyz-4,3536
numpy/typing/tests/data/pass/mod.py,sha256=L1qLwjdrRo9Tx7mxWpf_ugdKdUprDYhPRbCvQd5QjXY,1725
numpy/typing/tests/data/pass/modules.py,sha256=buzLurat4TIGmJuW3mGsGk7dKNmpBDfQOWWQXFfb9Uc,670
numpy/typing/tests/data/pass/multiarray.py,sha256=i6VU-VN96Q16mRGzVoY3oTE2W1z16GOGTOVFxWGRacM,1407
numpy/typing/tests/data/pass/ndarray_conversion.py,sha256=6TnvucV8Vtte7dGWihx7YmrHlNOanqmLJIH1W8Wok0E,1612
numpy/typing/tests/data/pass/ndarray_misc.py,sha256=UClzDh7wOt-ir3DqW7V_R8E455CXv0_KxLxrjVu1OiA,3897
numpy/typing/tests/data/pass/ndarray_shape_manipulation.py,sha256=yaBK3hW5fe2VpvARkn_NMeF-JX-OajI8JiRWOA_Uk7Y,687
numpy/typing/tests/data/pass/nditer.py,sha256=1wpRitCNZKCC3WJVrFSh22Z1D8jP2VxQAMtzH8NcpV8,67
numpy/typing/tests/data/pass/numeric.py,sha256=qOUXDYNR9gwmu0WWAEAjBhregmntipG43vX6a3abys8,1717
numpy/typing/tests/data/pass/numerictypes.py,sha256=JaCjk4zQPOI67XzqGyi3dI-GUMFM2AvDuniwzSQ7_Rk,348
numpy/typing/tests/data/pass/random.py,sha256=wYwClLry-mN-QvaYg6AFGhwDuvoKQv-bl94fq10sL3k,63321
numpy/typing/tests/data/pass/recfunctions.py,sha256=gxsaR6DLKQdHyxWrrt_8RFvR7CPIDoLDIJoFYKnOxfo,5164
numpy/typing/tests/data/pass/scalars.py,sha256=fsbpvW8PUGXFEn_QMQx-locj-KRuEHfTD47y4UhZ0Gs,3972
numpy/typing/tests/data/pass/shape.py,sha256=KjQMcyjXO5BXJGWdRqdgYYWQR9gneGTxQtg-jOsXJkk,464
numpy/typing/tests/data/pass/simple.py,sha256=aXvt9iCOV1lhQR11xVWgQIXXyXRHKOBfCtTjthZFtM0,2919
numpy/typing/tests/data/pass/simple_py3.py,sha256=OBpoDmf5u4bRblugokiOZzufESsEmoU03MqipERrjLg,102
numpy/typing/tests/data/pass/ufunc_config.py,sha256=gmMTPrq8gLXJZSBQoOpJcgzIzWgMx-k_etKPV4KSTJk,1269
numpy/typing/tests/data/pass/ufunclike.py,sha256=jxTR61d0bmFg7JHZmw992ccRua00u4XWJYtcQRJwFS0,1172
numpy/typing/tests/data/pass/ufuncs.py,sha256=gvdcCNoGUfN0CnQmn6k1j6ghdt8zGkJdcRcgctmU48A,438
numpy/typing/tests/data/pass/warnings_and_errors.py,sha256=q3c1SmMwhyYLYQsLjK02AXphk3-96YltSTdTfrElJzQ,167
numpy/typing/tests/data/reveal/arithmetic.pyi,sha256=WIhvUmtuZmP94MrcxtPcFcc04nwJ1EiniIc39BxKQLQ,28144
numpy/typing/tests/data/reveal/array_api_info.pyi,sha256=TOvbhGUiNYcKv9KQs0A4-vzM_l9vYWvDXoqBOOLo1d4,3087
numpy/typing/tests/data/reveal/array_constructors.pyi,sha256=tivg8EMpl-nKM9n6fj6tHafNDRt1G8EegVKVpoq7_M0,13179
numpy/typing/tests/data/reveal/arraypad.pyi,sha256=iE4OyZFrFjIXr46CzYaFzIkw-FR5RBV6v0OkCgFW2Ug,675
numpy/typing/tests/data/reveal/arrayprint.pyi,sha256=G5x1q4i4JqK8LjuvFkt2lC5mETW0arqs6lADgND37xk,802
numpy/typing/tests/data/reveal/arraysetops.pyi,sha256=-Fhtnyc-uzDT5zUlh7xSLmdkK05Y14MqpgdG38Xliu0,4485
numpy/typing/tests/data/reveal/arrayterator.pyi,sha256=ZPn6r0IvThiDma9RtpnpPywdQdg5iS9qacWUGLI-Mzw,1066
numpy/typing/tests/data/reveal/bitwise_ops.pyi,sha256=Sgc_xt5stNYxuWnwP52jYNCBLrfDQuPPOIxRgDMJvA4,5079
numpy/typing/tests/data/reveal/char.pyi,sha256=f0OYS8KfgzATtHj3sBDV2DPMfTModkwTKCrRpzhR0M8,10990
numpy/typing/tests/data/reveal/chararray.pyi,sha256=u-H9EUc1tci2R4rYQDOeb0sWI_gJNtIbwEO4LtpBRgA,5336
numpy/typing/tests/data/reveal/comparisons.pyi,sha256=rT61d7Th3UwWti-6gDXW-t2WAv4tQUxLzbv1w88ultw,7459
numpy/typing/tests/data/reveal/constants.pyi,sha256=DHycCQpNsu52JrhZ_Qds7f0F4U0rD4zWaVMOLwWR08o,347
numpy/typing/tests/data/reveal/ctypeslib.pyi,sha256=TuMyVAki_VnT2jYiCGPNOvb9W3iWGL2sKKkbKul_5P0,4215
numpy/typing/tests/data/reveal/datasource.pyi,sha256=07PFHAOF4kL5Wqq5pt1IKvx6VPYBZ9IK1_WXo_3II1E,606
numpy/typing/tests/data/reveal/dtype.pyi,sha256=Meno9OgmIzKXtMcm5-wG7PrSoNj7bw3Dpx2vk2t44w8,5216
numpy/typing/tests/data/reveal/einsumfunc.pyi,sha256=R-ve3Dda6S1ewKjlDoQ1XWx4Adj-JxFe9TFdrPeX2sA,1965
numpy/typing/tests/data/reveal/emath.pyi,sha256=HGPBuE6CTTSHirCCQMklDGkvamoIILqpREfP9NJh49I,2179
numpy/typing/tests/data/reveal/fft.pyi,sha256=uNGippaypaPH9ZmwaOXwPdHXDh_TW4BJlEkv9idK08Y,1638
numpy/typing/tests/data/reveal/flatiter.pyi,sha256=Pc-55PWa65noaoUN0i65W5A_KhsMlyQy7PmmWHhiEzw,1394
numpy/typing/tests/data/reveal/fromnumeric.pyi,sha256=AbQOzaqMl8Of2bh4i5JhPFlWGd3oQ8PfoFFkfhFyg60,15413
numpy/typing/tests/data/reveal/getlimits.pyi,sha256=5hwJKUUfd5M3OHV3pgic_HEZ4Ng4Pz_NhUrJkLiMFZE,1598
numpy/typing/tests/data/reveal/histograms.pyi,sha256=2l6Af9c4cru2C6xSuL0iqW78OqEo4nrWFqARC5nA0Xo,1282
numpy/typing/tests/data/reveal/index_tricks.pyi,sha256=UoYlUdqV2jgImmKFDOkLECoLN1ZuTWs7P0VLczBi8a8,3311
numpy/typing/tests/data/reveal/lib_function_base.pyi,sha256=aaj5lXESqp9f18l49gC36g7r8JpBUANeRAxvBTa5m0s,10325
numpy/typing/tests/data/reveal/lib_polynomial.pyi,sha256=7bNjEg_B-Zn8nR5rYpjRRdfvmMrZ8GdPsYOky-9sQpQ,5804
numpy/typing/tests/data/reveal/lib_utils.pyi,sha256=0mEaIvr9BkYx2Gmmj7M0vvG_o4c0TgD0uU8uJsY7Jl0,453
numpy/typing/tests/data/reveal/lib_version.pyi,sha256=5-H7IY5M-OT0Wu7d0FhO95s5jYafpMb2s-sYPNEisaQ,592
numpy/typing/tests/data/reveal/linalg.pyi,sha256=2FFoZaaoL8xOUyWMTNCxm_6pkIUBikjHdF3YwoP1URo,6065
numpy/typing/tests/data/reveal/ma.pyi,sha256=dHB3WtHewLDUabQLgoKS5Mn-AUr7g2cFrljVjgRl5x8,16668
numpy/typing/tests/data/reveal/matrix.pyi,sha256=lwsf__A8a4HSIYD_K34S_6Ee5U_QOSjoT3K6N7M4YUo,3113
numpy/typing/tests/data/reveal/memmap.pyi,sha256=KdMkvJgXmWwOAE6H0BKQjB-A3go2keJAem59FUJtmVM,738
numpy/typing/tests/data/reveal/mod.pyi,sha256=b2GnWlWl69iGDcIrVugp-5NDGjB0goPhbYFfDE81-sI,7779
numpy/typing/tests/data/reveal/modules.pyi,sha256=dgB-VZ5GQpABSsnAsnwfUx781fv7dia3TrDnguVippg,1909
numpy/typing/tests/data/reveal/multiarray.pyi,sha256=4Lwll_tpCpPpRBqKHZBqQ1nnxZKoxUxnaoxkb464LeA,7973
numpy/typing/tests/data/reveal/nbit_base_example.pyi,sha256=Wl_9To2CqowfTZ3bQjQX4XAAHlV2vQXhooTB8X6-TjI,708
numpy/typing/tests/data/reveal/ndarray_assignability.pyi,sha256=isvO6pgO2LILbb9tZcaKBCjSZpZEwxf3yKsrE8nuuIE,2745
numpy/typing/tests/data/reveal/ndarray_conversion.pyi,sha256=XVQD_AMblNmEfsbl0xkAIoKq5HPP7HDfWKZl31xoBuA,3394
numpy/typing/tests/data/reveal/ndarray_misc.pyi,sha256=cK53WG22QZY7IKMT7FVq3cz9jBr1riAE-Xuxw7TAsBs,8435
numpy/typing/tests/data/reveal/ndarray_shape_manipulation.pyi,sha256=k9inQc3sgIThhxPkGTWrc_phJlIIxtc3jyD03y3ss4I,1433
numpy/typing/tests/data/reveal/nditer.pyi,sha256=eHIBPj1I75kErxXsTi0CQEjcJ_WvHXIx7QzEeTmxYDk,1947
numpy/typing/tests/data/reveal/nested_sequence.pyi,sha256=aRkKbnRxI2A-xkg8iQZWOHU7672PlyMNpUHIkONymLw,637
numpy/typing/tests/data/reveal/npyio.pyi,sha256=tN3pkmY_sQ3PxBXuLM3QP_n4BTVpl-p9iYu88di7-KY,3576
numpy/typing/tests/data/reveal/numeric.pyi,sha256=cmCusW1Ey3X5o_dKImmJ6UmmH5VpbjFgqKxifn_G0Mc,6003
numpy/typing/tests/data/reveal/numerictypes.pyi,sha256=A2C0JnIxZZTrsu6jWu3fRTPm73zmyvqoAXWH3sBAl2Y,1382
numpy/typing/tests/data/reveal/polynomial_polybase.pyi,sha256=4cKgrsTFCWtUUOg_ubwlQUNLxDq1nrVJsx1ZPKrAM-Y,8145
numpy/typing/tests/data/reveal/polynomial_polyutils.pyi,sha256=bHSG-j7seKlhO9CWzq50lFMRYQ9xU8t_mO1iwbN9bfg,10861
numpy/typing/tests/data/reveal/polynomial_series.pyi,sha256=X8xFy5T7U-1XTCkWqbvDO3mbaPfNHL7w0zVBlgqtiDM,6991
numpy/typing/tests/data/reveal/random.pyi,sha256=JlNcrkvVoyI8uezvQ9BBCTtFoVQPRgZ5zPXKiDdCFwo,105842
numpy/typing/tests/data/reveal/rec.pyi,sha256=2Pr6-v4uSA_t13cYdLr2sKQSb1Ko9YuZQS-_UUAZwFo,3549
numpy/typing/tests/data/reveal/scalars.pyi,sha256=SkFaYVzb4mE3XwO9jyBJPKghThNG9QDeuQ0T30WmFhs,6569
numpy/typing/tests/data/reveal/shape.pyi,sha256=9IilbiRez0Lbu7Zv_HvqdEwQhjRLzcbnKm-4wnG6d9c,275
numpy/typing/tests/data/reveal/shape_base.pyi,sha256=1_G5HGC45IFPnetUyXXnyIhJYjNu0Mf7AEEtSz3QrmI,2058
numpy/typing/tests/data/reveal/stride_tricks.pyi,sha256=PqrKANjOAnn_CQ2CjERvonBbM89PjhZpgnekiQpT04k,1342
numpy/typing/tests/data/reveal/strings.pyi,sha256=D2r-lCrWhVM3HQ8X2cjLT2cwujxYLOb1ASrnHs2qPD8,9743
numpy/typing/tests/data/reveal/testing.pyi,sha256=Fq5bXxS7veDk7MtlBPXq4mqCYOo59iG9jkahpYclc88,8641
numpy/typing/tests/data/reveal/twodim_base.pyi,sha256=4OGoYVclmH23D9gykK2iUiVBk8KHH6tsVlVZ8-i2xCg,4382
numpy/typing/tests/data/reveal/type_check.pyi,sha256=3on6Yhb-vylW0Etlpl_VP6bevzBipPn8J8W6EVE66eU,2459
numpy/typing/tests/data/reveal/ufunc_config.pyi,sha256=81tK1vAXCeLENv81jQcubfgr_DUIDlzlwepVkEPIuWg,1192
numpy/typing/tests/data/reveal/ufunclike.pyi,sha256=sVSBqzrvFwjyOL2Jt08ZRyhJorAiLe9mzxEf5b8tIAE,1214
numpy/typing/tests/data/reveal/ufuncs.pyi,sha256=8hXQYiuZVsv5-5YU9NAXFSLbWKij8nG7ic8f2SfWn7o,4912
numpy/typing/tests/data/reveal/warnings_and_errors.pyi,sha256=kdpx5u0-zsWOPcsnciaqsACr7OKUuFWmMeMCizZDun8,460
numpy/typing/tests/test_isfile.py,sha256=mjzMMwseQ7AgjGpj9QSPpVnFfIjKhn_ExwEnsYrdQy8,910
numpy/typing/tests/test_runtime.py,sha256=NzisDEYOVKe6NASr9F2oxdxKMg4sVXbQBloYUvgDjkY,3021
numpy/typing/tests/test_typing.py,sha256=j6wK6nH2Jx9V8ijLJrr8QdJjDv3NbJKCrEUNF0j7AxE,6494
numpy/version.py,sha256=LnNkjfp3InQfmR23UjIRL0ctKRuzSBoGGGoVTz-Pvj0,304
numpy/version.pyi,sha256=evzpqH8S5BWrxwgxVoRGkeW6yV6etN9CAp1rnfRVq-s,376
