from PyQt5.QtCore import QObject, QTimer, pyqtSignal
from sqlalchemy.orm import Session
from sqlalchemy import func
from database.models import Product, Transaction, Customer, TransactionType
from datetime import datetime, timedelta
import numpy as np
from scipy import stats

class SmartAlertSystem(QObject):
    """نظام التنبيهات الذكي مع التحليل التنبؤي"""
    
    alert_signal = pyqtSignal(str, str, str)  # النوع، العنوان، الرسالة

    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.start_monitoring()

    def start_monitoring(self):
        """بدء المراقبة الدورية"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_alerts)
        self.timer.start(1800000)  # فحص كل 30 دقيقة
        self.check_alerts()  # فحص فوري عند البدء

    def check_alerts(self):
        """فحص جميع أنواع التنبيهات"""
        self.check_inventory_alerts()
        self.check_sales_alerts()
        self.check_customer_alerts()
        self.check_financial_alerts()

    def check_inventory_alerts(self):
        """فحص تنبيهات المخزون مع التنبؤ بالطلب"""
        with Session(self.engine) as session:
            products = session.query(Product).all()
            
            for product in products:
                # تحليل حركة المخزون
                sales_history = self.get_product_sales_history(session, product.id)
                if len(sales_history) > 0:
                    # التنبؤ بالطلب المستقبلي
                    forecast = self.forecast_demand(sales_history)
                    days_until_stockout = self.calculate_days_until_stockout(
                        product.quantity, forecast
                    )
                    
                    if days_until_stockout <= 7:
                        self.alert_signal.emit(
                            "warning",
                            "تنبيه المخزون",
                            f"المنتج {product.name} سينفد خلال {days_until_stockout} أيام"
                        )
                    
                    if product.quantity <= product.min_quantity:
                        self.alert_signal.emit(
                            "danger",
                            "مخزون منخفض",
                            f"المنتج {product.name} وصل للحد الأدنى ({product.quantity} متبقي)"
                        )

    def check_sales_alerts(self):
        """تحليل أنماط المبيعات واكتشاف الانحرافات"""
        with Session(self.engine) as session:
            # تحليل المبيعات اليومية
            today = datetime.now().date()
            past_week = today - timedelta(days=7)
            
            daily_sales = session.query(
                func.date(Transaction.date),
                func.sum(Transaction.total_amount)
            ).filter(
                Transaction.type == TransactionType.SALE,
                Transaction.date >= past_week
            ).group_by(
                func.date(Transaction.date)
            ).all()
            
            if daily_sales:
                amounts = [amount for _, amount in daily_sales]
                mean_sales = np.mean(amounts)
                std_sales = np.std(amounts)
                
                # اكتشاف الانحرافات الكبيرة
                latest_sales = amounts[-1]
                z_score = (latest_sales - mean_sales) / std_sales if std_sales > 0 else 0
                
                if z_score < -2:  # انخفاض كبير
                    self.alert_signal.emit(
                        "warning",
                        "انخفاض المبيعات",
                        f"انخفاض غير معتاد في المبيعات اليوم ({latest_sales:,.2f})"
                    )
                elif z_score > 2:  # ارتفاع كبير
                    self.alert_signal.emit(
                        "info",
                        "ارتفاع المبيعات",
                        f"ارتفاع ملحوظ في المبيعات اليوم ({latest_sales:,.2f})"
                    )

    def check_customer_alerts(self):
        """تحليل سلوك العملاء والكشف عن الأنماط"""
        with Session(self.engine) as session:
            customers = session.query(Customer).all()
            
            for customer in customers:
                # تحليل أنماط الشراء
                last_purchase = session.query(Transaction).filter(
                    Transaction.customer_id == customer.id,
                    Transaction.type == TransactionType.SALE
                ).order_by(
                    Transaction.date.desc()
                ).first()
                
                if last_purchase:
                    days_since_last_purchase = (datetime.now() - last_purchase.date).days
                    
                    # العملاء غير النشطين
                    if days_since_last_purchase > 90:  # 3 أشهر
                        self.alert_signal.emit(
                            "info",
                            "عميل غير نشط",
                            f"العميل {customer.name} لم يقم بالشراء منذ {days_since_last_purchase} يوم"
                        )
                    
                    # العملاء المتأخرون في السداد
                    if customer.balance > 0:
                        self.alert_signal.emit(
                            "warning",
                            "رصيد مستحق",
                            f"العميل {customer.name} عليه مبلغ {customer.balance:,.2f}"
                        )

    def check_financial_alerts(self):
        """تحليل المؤشرات المالية"""
        with Session(self.engine) as session:
            # حساب الأرباح الإجمالية
            total_sales = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.SALE
            ).scalar() or 0
            
            total_purchases = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.PURCHASE
            ).scalar() or 0
            
            gross_profit = total_sales - total_purchases
            profit_margin = (gross_profit / total_sales * 100) if total_sales > 0 else 0
            
            if profit_margin < 20:  # هامش ربح منخفض
                self.alert_signal.emit(
                    "danger",
                    "هامش ربح منخفض",
                    f"هامش الربح الحالي {profit_margin:.1f}% أقل من المستهدف"
                )

    def get_product_sales_history(self, session, product_id):
        """الحصول على سجل مبيعات المنتج"""
        month_ago = datetime.now() - timedelta(days=30)
        
        sales = session.query(
            func.date(Transaction.date),
            func.sum(TransactionItem.quantity)
        ).join(
            TransactionItem
        ).filter(
            Transaction.type == TransactionType.SALE,
            TransactionItem.product_id == product_id,
            Transaction.date >= month_ago
        ).group_by(
            func.date(Transaction.date)
        ).all()
        
        return [quantity for _, quantity in sales]

    def forecast_demand(self, history):
        """التنبؤ بالطلب المستقبلي باستخدام الانحدار الخطي البسيط"""
        if len(history) < 2:
            return np.mean(history) if len(history) > 0 else 0
            
        X = np.arange(len(history)).reshape(-1, 1)
        y = np.array(history)
        
        slope, intercept, _, _, _ = stats.linregress(X.flatten(), y)
        
        # التنبؤ لليوم التالي
        next_day = len(history)
        forecast = slope * next_day + intercept
        
        return max(0, forecast)  # لا يمكن أن يكون الطلب سالباً

    def calculate_days_until_stockout(self, current_stock, daily_demand):
        """حساب الأيام المتبقية حتى نفاد المخزون"""
        if daily_demand <= 0:
            return float('inf')
        return int(current_stock / daily_demand)

    def stop_monitoring(self):
        """إيقاف المراقبة"""
        if hasattr(self, 'timer'):
            self.timer.stop()