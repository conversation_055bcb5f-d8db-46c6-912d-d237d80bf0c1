@echo off
chcp 65001 > nul
title فتح البرنامج المُصلح
echo.
echo ========================================
echo    📁 فتح البرنامج المُصلح
echo ========================================
echo.

echo 🔍 البحث عن الملفات المُصلحة...
echo.

REM البحث عن الملفات التنفيذية المتاحة
if exist "نظام_المحاسبة_العصري_v2_مُصلح.exe" (
    echo ✅ تم العثور على: نظام_المحاسبة_العصري_v2_مُصلح.exe
    start "" "نظام_المحاسبة_العصري_v2_مُصلح.exe"
    goto :found
)

if exist "نظام_المحاسبة_العصري_v2_فوري.exe" (
    echo ✅ تم العثور على: نظام_المحاسبة_العصري_v2_فوري.exe
    start "" "نظام_المحاسبة_العصري_v2_فوري.exe"
    goto :found
)

if exist "نظام_المحاسبة_العصري_v2_سريع.exe" (
    echo ✅ تم العثور على: نظام_المحاسبة_العصري_v2_سريع.exe
    start "" "نظام_المحاسبة_العصري_v2_سريع.exe"
    goto :found
)

if exist "dist\نظام_المحاسبة_العصري_v2_1_عصري.exe" (
    echo ✅ تم العثور على: dist\نظام_المحاسبة_العصري_v2_1_عصري.exe
    start "" "dist\نظام_المحاسبة_العصري_v2_1_عصري.exe"
    goto :found
)

if exist "نظام_المحاسبة_العصري_v2_1_عصري_نهائي\نظام_المحاسبة_العصري_v2_1.exe" (
    echo ✅ تم العثور على: نظام_المحاسبة_العصري_v2_1_عصري_نهائي\نظام_المحاسبة_العصري_v2_1.exe
    start "" "نظام_المحاسبة_العصري_v2_1_عصري_نهائي\نظام_المحاسبة_العصري_v2_1.exe"
    goto :found
)

echo ❌ لم يتم العثور على أي ملف تنفيذي!
echo.
echo 💡 الملفات المتاحة:
dir *.exe /b 2>nul
echo.
echo 📁 المجلدات المتاحة:
dir نظام_* /b /ad 2>nul
echo.
pause
goto :end

:found
echo.
echo 🚀 تم تشغيل البرنامج بنجاح!
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    • اسم المستخدم: sicoo
echo    • كلمة المرور: sicoo123
echo.
echo ✅ الإصلاحات المطبقة:
echo    • إصلاح مشكلة عدم فتح البرنامج بعد تسجيل الدخول
echo    • لوجو الشركة المطورة (Sico Company)
echo    • تحسين واجهة تسجيل الدخول
echo.
echo 💡 الآن البرنامج سيفتح بعد تسجيل الدخول!

:end
timeout /t 8 > nul
