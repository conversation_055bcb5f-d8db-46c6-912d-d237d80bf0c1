#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء أصول الشركة المطورة
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_company_logo():
    """إنشاء لوجو الشركة المطورة"""
    print("🎨 إنشاء لوجو الشركة المطورة...")
    
    try:
        # إنشاء صورة بحجم 512x512
        img = Image.new('RGBA', (512, 512), (255, 255, 255, 0))
        draw = ImageDraw.Draw(img)
        
        # رسم دائرة خلفية بلون أزرق عصري
        draw.ellipse([20, 20, 492, 492], fill=(52, 152, 219, 255))
        
        # رسم دائرة داخلية بيضاء
        draw.ellipse([60, 60, 452, 452], fill=(255, 255, 255, 255))
        
        # رسم شعار المحاسبة
        # مستطيلات ملونة تمثل البيانات
        colors = [
            (52, 152, 219),   # أزرق
            (46, 204, 113),   # أخضر
            (231, 76, 60),    # أحمر
            (241, 196, 15),   # أصفر
            (155, 89, 182)    # بنفسجي
        ]
        
        # رسم مخطط بياني
        bar_width = 40
        bar_spacing = 20
        start_x = 150
        base_y = 380
        
        heights = [120, 180, 100, 160, 140]
        
        for i, (color, height) in enumerate(zip(colors, heights)):
            x = start_x + i * (bar_width + bar_spacing)
            y = base_y - height
            draw.rectangle([x, y, x + bar_width, base_y], fill=color)
        
        # رسم خط اتجاه صاعد
        points = []
        for i in range(5):
            x = start_x + i * (bar_width + bar_spacing) + bar_width // 2
            y = base_y - heights[i] - 20
            points.append((x, y))
        
        # رسم الخط
        for i in range(len(points) - 1):
            draw.line([points[i], points[i + 1]], fill=(52, 73, 94), width=4)
        
        # رسم نقاط على الخط
        for point in points:
            draw.ellipse([point[0] - 6, point[1] - 6, point[0] + 6, point[1] + 6], 
                        fill=(52, 73, 94))
        
        # حفظ اللوجو
        img.save('assets/company_logo.png', 'PNG')
        print("✅ تم إنشاء: assets/company_logo.png")
        
        # إنشاء نسخة أصغر للأيقونة
        icon_img = img.resize((256, 256), Image.Resampling.LANCZOS)
        icon_img.save('assets/company_icon.png', 'PNG')
        print("✅ تم إنشاء: assets/company_icon.png")
        
        # نسخ كأيقونة رئيسية
        shutil.copy2('assets/company_logo.png', 'assets/icons.png')
        print("✅ تم نسخ: assets/icons.png")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء اللوجو: {e}")
        return False

def main():
    """إنشاء أصول الشركة"""
    print("🚀 إنشاء أصول الشركة المطورة...")
    
    # إنشاء مجلد assets
    if not os.path.exists('assets'):
        os.makedirs('assets')
        print("✅ تم إنشاء مجلد assets")
    
    # إنشاء اللوجو
    if create_company_logo():
        print("🎉 تم إنشاء جميع أصول الشركة بنجاح!")
    else:
        print("❌ فشل في إنشاء أصول الشركة")

if __name__ == "__main__":
    import shutil
    main()
    input("اضغط Enter للخروج...")
