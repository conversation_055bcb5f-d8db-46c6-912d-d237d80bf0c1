from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLineEdit,
                           QPushButton, QLabel, QMessageBox, QFrame, QGraphicsDropShadowEffect)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QIcon, QColor
from sqlalchemy.orm import Session, joinedload
from database.users import User, Role
from datetime import datetime
import os

class ModernLoginDialog(QDialog):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.user = None
        
        # إعداد النافذة
        self.setWindowTitle("تسجيل الدخول - نظام المحاسبة العصري")
        self.setFixedSize(900, 600)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # تعيين أيقونة النافذة
        try:
            from main import resource_path
            icon_paths = [
                'assets/company_logo.png',
                'assets/company_icon.png',
                'assets/icons.png'
            ]
            for icon_file in icon_paths:
                icon_path = resource_path(icon_file)
                if os.path.exists(icon_path):
                    self.setWindowIcon(QIcon(icon_path))
                    break
        except:
            pass
        
        self.setup_ui()
        
    def setup_ui(self):
        # التخطيط الرئيسي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # الجانب الأيسر - معلومات الشركة
        left_frame = QFrame()
        left_frame.setFixedWidth(400)
        left_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-top-left-radius: 15px;
                border-bottom-left-radius: 15px;
            }
        """)
        
        left_layout = QVBoxLayout(left_frame)
        left_layout.setAlignment(Qt.AlignCenter)
        
        # لوجو الشركة المطورة
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        
        # محاولة تحميل لوجو الشركة المطورة
        try:
            from main import resource_path
            logo_paths = [
                'assets/company_logo.png',
                'assets/company_icon.png',
                'assets/logobar.png',
                'assets/icons.png'
            ]
            
            logo_loaded = False
            for logo_file in logo_paths:
                logo_path = resource_path(logo_file)
                if os.path.exists(logo_path):
                    pixmap = QPixmap(logo_path)
                    if not pixmap.isNull():
                        logo_label.setPixmap(pixmap.scaled(200, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation))
                        logo_loaded = True
                        break
            
            if not logo_loaded:
                logo_label.setText("🏢")
                logo_label.setStyleSheet("font-size: 80px; color: white;")
        except:
            logo_label.setText("🏢")
            logo_label.setStyleSheet("font-size: 80px; color: white;")
        
        left_layout.addWidget(logo_label)
        
        # اسم الشركة
        company_label = QLabel("Sico Company")
        company_label.setAlignment(Qt.AlignCenter)
        company_label.setStyleSheet("""
            color: white;
            font-size: 28px;
            font-weight: bold;
            margin: 10px;
        """)
        left_layout.addWidget(company_label)
        
        # وصف النظام
        desc_label = QLabel("نظام المحاسبة العصري\nالحل الشامل لإدارة أعمالك")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            line-height: 1.5;
        """)
        left_layout.addWidget(desc_label)
        
        # الجانب الأيمن - نموذج تسجيل الدخول
        right_frame = QFrame()
        right_frame.setFixedWidth(500)
        right_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-top-right-radius: 15px;
                border-bottom-right-radius: 15px;
            }
        """)
        
        right_layout = QVBoxLayout(right_frame)
        right_layout.setContentsMargins(50, 50, 50, 50)
        
        # عنوان تسجيل الدخول
        title_label = QLabel("تسجيل الدخول")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            color: #2c3e50;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 30px;
        """)
        right_layout.addWidget(title_label)
        
        # حقول الإدخال
        self.username = QLineEdit()
        self.username.setPlaceholderText("🔤 اسم المستخدم")
        self.username.setText("sicoo")
        self.username.setStyleSheet("""
            QLineEdit {
                padding: 15px;
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                font-size: 16px;
                margin-bottom: 15px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        right_layout.addWidget(self.username)
        
        self.password = QLineEdit()
        self.password.setPlaceholderText("🔒 كلمة المرور")
        self.password.setText("sicoo123")
        self.password.setEchoMode(QLineEdit.Password)
        self.password.setStyleSheet("""
            QLineEdit {
                padding: 15px;
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                font-size: 16px;
                margin-bottom: 20px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        right_layout.addWidget(self.password)
        
        # زر تسجيل الدخول
        login_btn = QPushButton("🚀 تسجيل الدخول")
        login_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2980b9, stop:1 #21618c);
            }
            QPushButton:pressed {
                background: #1f4e79;
            }
        """)
        login_btn.clicked.connect(self.login)
        right_layout.addWidget(login_btn)
        
        # مساحة فارغة
        right_layout.addStretch()
        
        # معلومات حقوق الطبع
        copyright_label = QLabel("© 2024 Sico Company - جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            color: #7f8c8d;
            font-size: 12px;
        """)
        right_layout.addWidget(copyright_label)
        
        # إضافة الإطارات للتخطيط الرئيسي
        main_layout.addWidget(left_frame)
        main_layout.addWidget(right_frame)
        
        self.setLayout(main_layout)
        
        # إضافة ظل للنافذة
        try:
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(20)
            shadow.setColor(QColor(0, 0, 0, 80))
            shadow.setOffset(0, 0)
            self.setGraphicsEffect(shadow)
        except:
            pass
        
    def login(self):
        username = self.username.text()
        password = self.password.text()

        if not username or not password:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال اسم المستخدم وكلمة المرور")
            return

        try:
            with Session(self.engine) as session:
                user = session.query(User).options(
                    joinedload(User.roles).joinedload(Role.permissions)
                ).filter(User.username == username).first()
                
                if user and user.check_password(password):
                    if not user.is_active:
                        QMessageBox.warning(self, "خطأ", "هذا الحساب غير نشط")
                        return
                        
                    user.last_login = datetime.now()
                    session.commit()
                    
                    session.refresh(user)
                    self.user = user
                    print(f"✅ تم تسجيل دخول المستخدم: {user.username}")
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تسجيل الدخول: {str(e)}")
            print(f"Login error: {e}")

# استخدام التصميم الجديد
LoginDialog = ModernLoginDialog
