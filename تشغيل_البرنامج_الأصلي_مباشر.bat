@echo off
chcp 65001 > nul
title تشغيل البرنامج الأصلي مباشر
echo.
echo ========================================
echo    🏢 نظام المحاسبة العصري v2
echo    🎨 التصميم الأصلي مع company01_logo.png
echo    🚀 تشغيل مباشر
echo ========================================
echo.

echo 🔍 البحث عن الملف التنفيذي...

if exist "dist\نظام_المحاسبة_العصري_v2_أصلي.exe" (
    echo ✅ تم العثور على: dist\نظام_المحاسبة_العصري_v2_أصلي.exe
    echo 🚀 تشغيل البرنامج...
    start "" "dist\نظام_المحاسبة_العصري_v2_أصلي.exe"
    goto :success
)

if exist "نظام_المحاسبة_العصري_v2_فوري.exe" (
    echo ✅ تم العثور على: نظام_المحاسبة_العصري_v2_فوري.exe
    echo 🚀 تشغيل البرنامج...
    start "" "نظام_المحاسبة_العصري_v2_فوري.exe"
    goto :success
)

if exist "نظام_المحاسبة_العصري_v2_سريع.exe" (
    echo ✅ تم العثور على: نظام_المحاسبة_العصري_v2_سريع.exe
    echo 🚀 تشغيل البرنامج...
    start "" "نظام_المحاسبة_العصري_v2_سريع.exe"
    goto :success
)

echo ❌ لم يتم العثور على أي ملف تنفيذي!
echo.
echo 📁 الملفات المتاحة:
dir *.exe /b 2>nul
echo.
echo 📁 مجلد dist:
dir dist\*.exe /b 2>nul
pause
goto :end

:success
echo.
echo ✅ تم تشغيل البرنامج بنجاح!
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    • اسم المستخدم: sicoo
echo    • كلمة المرور: sicoo123
echo.
echo ✅ الميزات:
echo    • التصميم الأصلي مع لوجو company01_logo.png
echo    • إصلاح مشكلة عدم فتح البرنامج بعد تسجيل الدخول
echo    • جميع الإصلاحات السابقة مطبقة
echo.
echo 💡 الآن البرنامج سيفتح بعد تسجيل الدخول!

:end
timeout /t 5 > nul
