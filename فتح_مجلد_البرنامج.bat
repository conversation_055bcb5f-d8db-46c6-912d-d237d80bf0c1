@echo off
chcp 65001 > nul
title فتح مجلد البرنامج النهائي
echo.
echo ========================================
echo    📁 فتح مجلد البرنامج النهائي
echo ========================================
echo.

set "folder_name=نظام_المحاسبة_العصري_v2_النهائي"

if exist "%folder_name%" (
    echo ✅ تم العثور على المجلد: %folder_name%
    echo 🚀 جاري فتح المجلد...
    start "" "%folder_name%"
    echo.
    echo 💡 المجلد مفتوح الآن!
    echo 📋 الملفات الموجودة:
    echo    • نظام_المحاسبة_العصري_v2.exe
    echo    • تشغيل_البرنامج.bat
    echo    • دليل_المستخدم.txt
    echo.
) else (
    echo ❌ لم يتم العثور على المجلد: %folder_name%
    echo.
    echo 🔍 جاري البحث في المجلد الحالي...
    dir /b | findstr "نظام_المحاسبة"
    echo.
    echo 💡 تأكد من أنك في المجلد الصحيح:
    echo    C:\Users\<USER>\OneDrive\Desktop\A5R UPDATE\
    echo.
    pause
)

timeout /t 3 > nul
