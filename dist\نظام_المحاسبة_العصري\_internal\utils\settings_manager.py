#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير إعدادات التطبيق
"""

from sqlalchemy.orm import Session
from database.settings_models import AppSettings, SUPPORTED_CURRENCIES, SUPPORTED_LANGUAGES, SUPPORTED_THEMES
import json

class SettingsManager:
    """مدير إعدادات التطبيق"""
    
    def __init__(self, engine):
        self.engine = engine
        self._cache = {}
        self.load_all_settings()
    
    def load_all_settings(self):
        """تحميل جميع الإعدادات في الذاكرة المؤقتة"""
        try:
            with Session(self.engine) as session:
                settings = session.query(AppSettings).all()
                self._cache = {}
                for setting in settings:
                    self._cache[setting.setting_key] = self._convert_value(
                        setting.setting_value, setting.setting_type
                    )
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            self._cache = {}
    
    def _convert_value(self, value, value_type):
        """تحويل القيمة حسب النوع"""
        if value is None:
            return None
            
        if value_type == 'boolean':
            return value.lower() in ('true', '1', 'yes', 'on')
        elif value_type == 'integer':
            try:
                return int(value)
            except (ValueError, TypeError):
                return 0
        elif value_type == 'json':
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return {}
        else:  # string
            return str(value)
    
    def get(self, key, default=None):
        """الحصول على قيمة إعداد"""
        return self._cache.get(key, default)
    
    def set(self, key, value, save_immediately=True):
        """تعيين قيمة إعداد"""
        self._cache[key] = value
        
        if save_immediately:
            self.save_setting(key, value)
    
    def save_setting(self, key, value):
        """حفظ إعداد في قاعدة البيانات"""
        try:
            with Session(self.engine) as session:
                setting = session.query(AppSettings).filter(
                    AppSettings.setting_key == key
                ).first()
                
                if setting:
                    # تحديث الإعداد الموجود
                    if isinstance(value, bool):
                        setting.setting_value = 'true' if value else 'false'
                    elif isinstance(value, (dict, list)):
                        setting.setting_value = json.dumps(value)
                    else:
                        setting.setting_value = str(value)
                else:
                    # إنشاء إعداد جديد
                    value_type = 'boolean' if isinstance(value, bool) else \
                                'integer' if isinstance(value, int) else \
                                'json' if isinstance(value, (dict, list)) else 'string'
                    
                    setting = AppSettings(
                        setting_key=key,
                        setting_value=str(value),
                        setting_type=value_type,
                        category='custom'
                    )
                    session.add(setting)
                
                session.commit()
                return True
        except Exception as e:
            print(f"خطأ في حفظ الإعداد {key}: {e}")
            return False
    
    def get_currency_info(self):
        """الحصول على معلومات العملة الحالية"""
        currency_code = self.get('currency', 'EGP')
        currency_info = SUPPORTED_CURRENCIES.get(currency_code, SUPPORTED_CURRENCIES['EGP'])

        return {
            'code': currency_code,
            'name': currency_info['name'],
            'symbol': self.get('currency_symbol', currency_info['symbol']),
            'position': self.get('currency_position', 'after'),
            'decimal_places': self.get('decimal_places', 0)  # تغيير الافتراضي إلى 0
        }
    
    def format_currency(self, amount):
        """تنسيق المبلغ بالعملة الحالية"""
        currency_info = self.get_currency_info()
        
        # تنسيق الرقم
        formatted_amount = f"{amount:,.{currency_info['decimal_places']}f}"
        
        # إضافة رمز العملة
        if currency_info['position'] == 'before':
            return f"{currency_info['symbol']} {formatted_amount}"
        else:
            return f"{formatted_amount} {currency_info['symbol']}"
    
    def get_language_info(self):
        """الحصول على معلومات اللغة الحالية"""
        language_code = self.get('language', 'ar')
        return SUPPORTED_LANGUAGES.get(language_code, SUPPORTED_LANGUAGES['ar'])
    
    def get_theme_info(self):
        """الحصول على معلومات المظهر الحالي"""
        theme_code = self.get('theme', 'light')
        return SUPPORTED_THEMES.get(theme_code, SUPPORTED_THEMES['light'])
    
    def get_shortcut(self, action):
        """الحصول على اختصار لوحة المفاتيح"""
        shortcut_key = f"{action}_shortcut"
        return self.get(shortcut_key, 'Ctrl+T')  # افتراضي
    
    def set_shortcut(self, action, shortcut):
        """تعيين اختصار لوحة المفاتيح"""
        shortcut_key = f"{action}_shortcut"
        self.set(shortcut_key, shortcut)
    
    def get_all_shortcuts(self):
        """الحصول على جميع الاختصارات"""
        shortcuts = {}
        for key in self._cache:
            if key.endswith('_shortcut'):
                action = key.replace('_shortcut', '')
                shortcuts[action] = self._cache[key]
        return shortcuts
    
    def reset_to_defaults(self):
        """إعادة تعيين جميع الإعدادات للقيم الافتراضية"""
        from database.settings_models import DEFAULT_SETTINGS
        
        try:
            with Session(self.engine) as session:
                # حذف جميع الإعدادات الحالية
                session.query(AppSettings).delete()
                
                # إدراج الإعدادات الافتراضية
                for key, config in DEFAULT_SETTINGS.items():
                    setting = AppSettings(
                        setting_key=key,
                        setting_value=config['value'],
                        setting_type=config['type'],
                        description=config['description'],
                        category=config['category']
                    )
                    session.add(setting)
                
                session.commit()
                
                # إعادة تحميل الإعدادات
                self.load_all_settings()
                return True
        except Exception as e:
            print(f"خطأ في إعادة تعيين الإعدادات: {e}")
            return False
    
    def export_settings(self, file_path):
        """تصدير الإعدادات إلى ملف"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self._cache, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في تصدير الإعدادات: {e}")
            return False
    
    def import_settings(self, file_path):
        """استيراد الإعدادات من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # حفظ الإعدادات المستوردة
            for key, value in imported_settings.items():
                self.set(key, value, save_immediately=False)
            
            # حفظ جميع الإعدادات دفعة واحدة
            with Session(self.engine) as session:
                for key, value in imported_settings.items():
                    setting = session.query(AppSettings).filter(
                        AppSettings.setting_key == key
                    ).first()
                    
                    if setting:
                        setting.setting_value = str(value)
                    else:
                        setting = AppSettings(
                            setting_key=key,
                            setting_value=str(value),
                            setting_type='string',
                            category='imported'
                        )
                        session.add(setting)
                
                session.commit()
            
            return True
        except Exception as e:
            print(f"خطأ في استيراد الإعدادات: {e}")
            return False

# مثيل عام لمدير الإعدادات
settings_manager = None

def get_settings_manager(engine=None):
    """الحصول على مثيل مدير الإعدادات"""
    global settings_manager
    if settings_manager is None and engine is not None:
        settings_manager = SettingsManager(engine)
    return settings_manager

def init_settings_manager(engine):
    """تهيئة مدير الإعدادات"""
    global settings_manager
    settings_manager = SettingsManager(engine)
    return settings_manager
