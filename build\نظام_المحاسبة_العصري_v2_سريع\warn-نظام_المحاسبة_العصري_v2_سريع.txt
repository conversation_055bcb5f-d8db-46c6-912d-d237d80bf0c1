
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named urllib.unquote_plus - imported by urllib (conditional), sqlalchemy.util.compat (conditional)
missing module named urllib.unquote - imported by urllib (conditional), sqlalchemy.util.compat (conditional)
missing module named urllib.quote_plus - imported by urllib (conditional), sqlalchemy.util.compat (conditional)
missing module named urllib.quote - imported by urllib (conditional), sqlalchemy.util.compat (conditional)
missing module named 'collections.abc' - imported by traceback (top-level), inspect (top-level), logging (top-level), typing (top-level), selectors (top-level), tracemalloc (top-level), setuptools (top-level), setuptools._distutils.filelist (top-level), setuptools._distutils.util (top-level), setuptools._vendor.jaraco.functools (top-level), setuptools._vendor.more_itertools.more (top-level), setuptools._vendor.more_itertools.recipes (top-level), setuptools._distutils._modified (top-level), setuptools._distutils.compat (top-level), setuptools._distutils.spawn (top-level), typing_extensions (top-level), asyncio.base_events (top-level), http.client (top-level), asyncio.coroutines (top-level), setuptools._distutils.compilers.C.base (top-level), setuptools._distutils.fancy_getopt (top-level), setuptools._reqs (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools._distutils.command.bdist (top-level), setuptools._distutils.core (top-level), setuptools._distutils.cmd (top-level), setuptools._distutils.dist (top-level), configparser (top-level), setuptools._distutils.extension (top-level), setuptools.config.setupcfg (top-level), setuptools.config.expand (top-level), setuptools.config.pyprojecttoml (top-level), setuptools.config._apply_pyprojecttoml (top-level), tomllib._parser (top-level), setuptools._vendor.tomli._parser (top-level), setuptools.command.egg_info (top-level), setuptools._distutils.command.build (top-level), setuptools._distutils.command.sdist (top-level), setuptools.glob (top-level), setuptools.command._requirestxt (top-level), setuptools.command.bdist_wheel (top-level), setuptools._vendor.wheel.cli.convert (top-level), setuptools._vendor.wheel.cli.tags (top-level), sqlalchemy.util.compat (conditional), werkzeug.wrappers.request (top-level), werkzeug.datastructures.accept (top-level), werkzeug.datastructures.structures (top-level), markupsafe (top-level), werkzeug.datastructures.cache_control (top-level), werkzeug.datastructures.mixins (top-level), werkzeug.datastructures.auth (top-level), werkzeug.datastructures.csp (top-level), werkzeug.datastructures.etag (top-level), werkzeug.datastructures.file_storage (top-level), werkzeug.datastructures.headers (top-level), werkzeug.datastructures.range (top-level), cryptography.utils (top-level), cryptography.x509.name (top-level), cryptography.x509.base (top-level), cryptography.hazmat.bindings.openssl.binding (top-level), cryptography.x509.extensions (top-level), werkzeug.middleware.shared_data (top-level), cryptography.fernet (top-level), numpy._typing._array_like (top-level), numpy._typing._nested_sequence (conditional), numpy._typing._shape (top-level), numpy._typing._dtype_like (top-level), numpy.lib._function_base_impl (top-level), numpy.lib._npyio_impl (top-level), numpy.random._common (top-level), numpy.random._generator (top-level), numpy.random.bit_generator (top-level), numpy.random.mtrand (top-level), numpy.polynomial._polybase (top-level), PIL.Image (top-level), PIL._typing (top-level), xml.etree.ElementTree (top-level), PIL.TiffImagePlugin (top-level), PIL.ImageOps (top-level), PIL.ImagePalette (top-level), PIL.ImageFilter (top-level), PIL.PngImagePlugin (top-level), fontTools.misc.loggingTools (top-level), fontTools.ttLib.ttGlyphSet (top-level), fontTools.ttLib.tables.S_V_G_ (top-level), fontTools.misc.plistlib (top-level), fontTools.ttLib.tables._h_d_m_x (top-level), fontTools.ttLib.tables._t_r_a_k (top-level), fontTools.misc.macRes (top-level), matplotlib (top-level), matplotlib.cbook (top-level), matplotlib._path (top-level), matplotlib.colors (top-level), pyparsing.core (top-level), pyparsing.results (top-level), cycler (top-level), matplotlib.cm (top-level), matplotlib.markers (top-level), matplotlib._mathtext (conditional), matplotlib.axes._base (top-level), matplotlib.spines (top-level), matplotlib.pyplot (conditional), matplotlib.typing (top-level), sqlite3.dbapi2 (top-level), pandas._typing (top-level), pytz.lazy (optional), pandas.util._exceptions (conditional), pandas._config.config (conditional), pandas.util.version (top-level), pandas.core.dtypes.inference (conditional), pandas.util._validators (top-level), pandas.core.construction (top-level), pandas.core.common (top-level), pandas.util._decorators (conditional), pandas.core.frame (top-level), pandas.core.dtypes.concat (conditional), pandas.core.sorting (conditional), pandas.core.indexes.category (conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.masked (conditional), pandas.core.apply (conditional), pandas.core.base (conditional), pandas.core.indexing (conditional), pandas.core.internals.blocks (conditional), pandas.core.arrays.numeric (conditional), pandas.core.arrays.timedeltas (conditional), pandas.io.formats.format (top-level), pandas.core.indexes.range (top-level), pandas.core.tools.timedeltas (conditional), pandas.core.indexes.datetimelike (conditional), pandas.core.reshape.concat (conditional), pandas.io.common (top-level), pandas.io.formats.printing (top-level), pandas.core.indexes.multi (top-level), pandas.io.formats.html (conditional), pandas.io.formats.string (conditional), pandas.io.formats.csvs (top-level), pandas.io.formats.style_render (top-level), pandas.core.interchange.dataframe_protocol (conditional), pandas.core.window.rolling (conditional), pandas.core.series (top-level), pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (conditional), pandas.core.methods.selectn (top-level), pandas.core.strings.accessor (conditional), pandas.core.tools.datetimes (conditional), pandas.io.formats.info (conditional), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.core.groupby.grouper (conditional), pandas.core.groupby.ops (conditional), pandas.io._util (conditional), pandas.io.json._normalize (conditional), pandas.io.parsers.base_parser (conditional), pandas.io.parsers.c_parser_wrapper (conditional), pandas.io.parsers.python_parser (top-level), pandas.io.parsers.readers (conditional), pandas.io.json._json (conditional), pandas.io.stata (conditional), pandas.io.formats.style (conditional), pandas.io.formats.excel (top-level), pandas.io.formats.css (conditional), pandas.io.excel._base (top-level), pandas.io.excel._util (top-level), pandas.core.arrays.interval (conditional), pandas.core.indexes.interval (conditional), pandas.core.arrays.period (conditional), pandas.core.indexes.period (conditional), pandas.core.internals.managers (top-level), pandas.core.internals.ops (conditional), pandas.core.internals.array_manager (conditional), pandas.core.internals.construction (conditional), pandas.core.methods.describe (conditional), pandas.core.generic (conditional), pandas.core.computation.parsing (conditional), pandas.compat.pickle_compat (conditional), pandas.core.computation.ops (conditional), pandas.core.computation.align (conditional), pandas.io.pytables (conditional), pandas.io.sql (conditional), pandas.core.groupby.groupby (top-level), pandas.core.strings.base (conditional), pandas.core.strings.object_array (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.groupby.base (conditional), pandas.core.groupby.indexing (top-level), pandas.core.resample (conditional), pandas.core.groupby.generic (conditional), pandas.core.reshape.merge (top-level), pandas.core.arrays.arrow.array (conditional), pandas.core.arrays.datetimelike (conditional), pandas.core.arrays.datetimes (conditional), pandas.core.indexes.datetimes (conditional), pandas.core.arrays._mixins (conditional), pandas.core.arrays.categorical (conditional), pandas.core.reshape.melt (conditional), pandas.core.interchange.dataframe (conditional), pandas.io.feather_format (conditional), pandas.io.xml (conditional), pandas.core.reshape.pivot (top-level), pandas.core.arrays.base (conditional), pandas.core.internals.concat (conditional), pandas.core.indexes.base (conditional), pandas.core.arrays.numpy_ (conditional), pandas.core.dtypes.cast (conditional), pandas.core.arrays.arrow.accessors (conditional), pandas.core.dtypes.dtypes (conditional), pandas.core.util.hashing (conditional), pandas.core.reshape.encoding (top-level), pandas._config.localization (conditional), pandas._testing.contexts (conditional), pandas._testing._warnings (conditional), pandas.io.html (conditional), pandas.io.sas.sasreader (conditional), pandas.io.spss (conditional), seaborn._core.typing (top-level), seaborn._base (top-level), seaborn._core.data (top-level), seaborn.external.docscrape (top-level), seaborn._core.scales (top-level), seaborn._core.plot (top-level), seaborn._marks.base (top-level), seaborn._stats.base (top-level), seaborn._core.subplots (top-level), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.converter (conditional), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.groupby (conditional), pandas.plotting._matplotlib.boxplot (conditional), PIL.Jpeg2KImagePlugin (top-level), PIL.IptcImagePlugin (top-level), setuptools._distutils.command.build_ext (top-level), _pyrepl.types (top-level), _pyrepl.readline (top-level), setuptools._distutils.compilers.C.msvc (top-level)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._distutils.util (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), http.server (delayed, optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), _pyrepl.unix_console (delayed, optional)
missing module named resource - imported by posix (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), _pyrepl.unix_console (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level), _pyrepl.pager (delayed, optional), werkzeug._reloader (delayed, optional), _pyrepl.unix_console (top-level), _pyrepl.fancy_termios (top-level), _pyrepl.unix_eventqueue (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.Value - imported by multiprocessing (top-level), werkzeug.debug (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level), _pyrepl.curses (optional)
missing module named readline - imported by code (delayed, conditional, optional), cmd (delayed, conditional, optional), rlcompleter (optional), pdb (delayed, optional), site (delayed, optional), sqlite3.__main__ (delayed, conditional, optional)
missing module named _typeshed - imported by setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), numpy.random.bit_generator (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\OneDrive\Desktop\Accounts_3\venv311\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named _suggestions - imported by traceback (delayed, optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional)
missing module named sip - imported by matplotlib.backends.qt_compat (delayed, conditional), PyQt5 (top-level)
missing module named AppKit - imported by pyperclip (delayed, conditional, optional), pandas.io.clipboard (delayed, conditional, optional)
missing module named Foundation - imported by pyperclip (delayed, conditional, optional), pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pyperclip (delayed, conditional, optional), pandas.io.clipboard (delayed, conditional, optional)
missing module named cPickle - imported by sqlalchemy.util.compat (conditional, optional)
missing module named urlparse - imported by sqlalchemy.util.compat (conditional)
missing module named cStringIO - imported by sqlalchemy.util.compat (conditional)
missing module named StringIO - imported by sqlalchemy.util.compat (conditional), six (conditional)
missing module named ConfigParser - imported by sqlalchemy.util.compat (conditional)
missing module named dummy_threading - imported by sqlalchemy.util.compat (optional)
missing module named pyodbc - imported by gui.easacc_importer (optional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), pandas.core._numba.extensions (top-level)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.arrays.arrow.accessors (conditional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named pyarrow - imported by pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.masked (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.interchange.utils (delayed, conditional), pandas.core.strings.accessor (delayed, conditional), pandas.io._util (conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.methods.describe (delayed, conditional), pandas.io.sql (delayed, conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.interchange.buffer (conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.dtypes.cast (delayed, conditional), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays.arrow.accessors (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas.core.reshape.encoding (delayed, conditional), pandas._testing (conditional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._array_utils_impl (top-level), numpy (conditional), numpy.fft._helper (top-level), numpy.fft._pocketfft (top-level)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._function_base_impl (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy.lib._utils_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longlong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named 'scipy.stats' - imported by pandas.core.nanops (delayed, conditional), seaborn._statistics (optional), seaborn._stats.density (optional), pandas.plotting._matplotlib.misc (delayed, conditional), pandas.plotting._matplotlib.hist (delayed)
missing module named scipy - imported by pandas.core.dtypes.common (delayed, conditional, optional), pandas.core.missing (delayed)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional)
missing module named 'IPython.core' - imported by pandas.io.formats.printing (delayed, conditional)
missing module named IPython - imported by pandas.io.formats.printing (delayed)
missing module named jinja2 - imported by pyparsing.diagram (top-level), pandas.io.formats.style (top-level)
missing module named defusedxml - imported by PIL.Image (optional), openpyxl.xml (delayed, optional)
missing module named 'defusedxml.ElementTree' - imported by openpyxl.xml.functions (conditional)
missing module named 'lxml.etree' - imported by openpyxl.xml (delayed, optional), openpyxl.xml.functions (conditional), pandas.io.xml (delayed), pandas.io.formats.xml (delayed), pandas.io.html (delayed)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named PIL._avif - imported by PIL (optional), PIL.AvifImagePlugin (optional)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed, conditional), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named 'tornado.template' - imported by matplotlib.backends.backend_webagg (delayed)
missing module named 'tornado.websocket' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.ioloop' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.web' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named tornado - imported by matplotlib.backends.backend_webagg (optional), matplotlib.backends.backend_webagg_core (delayed)
missing module named gi - imported by matplotlib.cbook (delayed, conditional)
missing module named numpy.VisibleDeprecationWarning - imported by numpy (optional), matplotlib.cbook (optional)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level)
missing module named certifi - imported by matplotlib (delayed, optional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional, optional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named sets - imported by reportlab.platypus.doctemplate (optional), pytz.tzinfo (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named 'scipy.sparse' - imported by pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (delayed, conditional), pandas.core.arrays.sparse.accessor (delayed)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'qtpy.QtWidgets' - imported by pandas.io.clipboard (delayed, optional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named lxml - imported by fontTools.misc.etree (optional), pandas.io.xml (conditional)
missing module named 'pyarrow.fs' - imported by pandas.io.orc (conditional)
missing module named fsspec - imported by pandas.io.orc (conditional)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed)
missing module named google - imported by pandas.io.gbq (conditional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named charset_normalizer - imported by numpy.f2py.crackfortran (optional)
missing module named yaml - imported by numpy.__config__ (delayed)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named bs4 - imported by pandas.io.html (delayed)
missing module named pytest - imported by pandas._testing._io (delayed), pandas._testing (delayed)
missing module named ipywidgets - imported by seaborn.widgets (optional)
missing module named sphinx - imported by seaborn.external.docscrape (delayed, conditional)
missing module named fastcluster - imported by seaborn.matrix (delayed)
missing module named 'scipy.cluster' - imported by seaborn.matrix (optional)
missing module named 'statsmodels.nonparametric' - imported by seaborn.regression (delayed)
missing module named 'statsmodels.tools' - imported by seaborn.regression (delayed)
missing module named 'statsmodels.robust' - imported by seaborn.regression (delayed, conditional)
missing module named 'statsmodels.genmod' - imported by seaborn.regression (delayed, conditional)
missing module named statsmodels - imported by seaborn.regression (optional)
missing module named 'win32com.shell' - imported by seaborn.external.appdirs (conditional, optional)
missing module named 'com.sun' - imported by seaborn.external.appdirs (delayed, conditional, optional)
missing module named com - imported by seaborn.external.appdirs (delayed)
missing module named win32api - imported by seaborn.external.appdirs (delayed, conditional, optional)
missing module named win32com - imported by seaborn.external.appdirs (delayed)
missing module named shiboken2 - imported by matplotlib.backends.qt_compat (delayed, conditional, optional)
missing module named shiboken6 - imported by matplotlib.backends.qt_compat (delayed, conditional)
missing module named gui.notifications_settings - imported by gui.notifications (delayed, optional)
missing module named fontTools.ttLib.TTFont - imported by fontTools.ttLib (top-level), fontTools.varLib (top-level), fontTools.cffLib (top-level), fontTools.cffLib.CFFToCFF2 (top-level), fontTools.cffLib.CFF2ToCFF (top-level), fontTools.cffLib.width (top-level), fontTools.ttLib.ttVisitor (top-level), fontTools.varLib.varStore (delayed), fontTools.otlLib.optimize.gpos (top-level), fontTools.otlLib.optimize (top-level), fontTools.varLib.stat (top-level), fontTools.colorLib.unbuilder (conditional), fontTools.cffLib.specializer (conditional), fontTools.ttx (top-level), fontTools.ttLib.woff2 (top-level), arabic_reshaper.reshaper_config (optional)
missing module named uharfbuzz - imported by fontTools.ttLib.tables.otBase (optional)
missing module named unicodedata2 - imported by fontTools.unicode (delayed, optional)
missing module named xattr - imported by fontTools.misc.macCreatorType (optional)
missing module named cython - imported by fontTools.varLib.iup (optional), fontTools.misc.bezierTools (optional)
missing module named 'lz4.block' - imported by fontTools.ttLib.tables.grUtils (optional)
missing module named lz4 - imported by fontTools.ttLib.tables.grUtils (optional)
missing module named zopfli - imported by fontTools.ttLib.sfnt (delayed, conditional)
missing module named brotli - imported by fontTools.ttLib.woff2 (optional)
missing module named brotlicffi - imported by fontTools.ttLib.woff2 (optional)
missing module named fontTools.ttLib.getTableClass - imported by fontTools.ttLib (top-level), fontTools.ttLib.woff2 (top-level)
missing module named fontTools.ttLib.getTableModule - imported by fontTools.ttLib (top-level), fontTools.ttLib.woff2 (top-level)
missing module named fontTools.ttLib.getSearchRange - imported by fontTools.ttLib (top-level), fontTools.ttLib.tables.otConverters (top-level), fontTools.ttLib.tables._c_m_a_p (top-level), fontTools.ttLib.tables._k_e_r_n (top-level), fontTools.ttLib.woff2 (top-level), fontTools.ttLib.sfnt (delayed, conditional)
missing module named fontTools.ttLib.getClassTag - imported by fontTools.ttLib (top-level), fontTools.ttLib.tables.DefaultTable (top-level)
missing module named fontTools.ttLib.newTable - imported by fontTools.ttLib (top-level), fontTools.varLib (top-level), fontTools.cffLib.CFFToCFF2 (top-level), fontTools.cffLib.CFF2ToCFF (top-level), fontTools.ttLib.tables._n_a_m_e (top-level), fontTools.varLib.featureVars (top-level), fontTools.varLib.cff (top-level)
missing module named 'rlextra.pageCatcher' - imported by reportlab.lib.pdfencrypt (delayed, optional)
missing module named pyaes - imported by reportlab.lib.pdfencrypt (optional)
missing module named pyphen - imported by reportlab.platypus.paragraph (optional)
missing module named reportlab.platypus.cleanBlockQuotedText - imported by reportlab.platypus (conditional), reportlab.platypus.paraparser (conditional)
missing module named tests - imported by reportlab.platypus.tables (conditional)
missing module named renderPM - imported by reportlab.graphics.charts.utils (delayed, conditional)
missing module named rlextra - imported by reportlab.graphics.charts.textlabels (optional)
missing module named freetype - imported by reportlab.graphics.utils (delayed, conditional, optional)
missing module named _rl_renderPM - imported by reportlab.graphics.utils (delayed, conditional, optional), reportlab.graphics.renderPM (delayed, conditional, optional)
missing module named 'rlextra.graphics' - imported by reportlab.graphics.shapes (delayed, conditional, optional)
missing module named Image - imported by reportlab.graphics.renderPM (delayed, optional)
missing module named rlPyCairo - imported by reportlab.graphics.renderPM (delayed, conditional, optional)
missing module named macostools - imported by reportlab.lib.utils (conditional), reportlab.graphics.renderPDF (delayed, conditional, optional), reportlab.graphics.shapes (delayed, conditional)
missing module named macfs - imported by reportlab.lib.utils (conditional), reportlab.graphics.renderPDF (delayed, conditional, optional), reportlab.graphics.shapes (delayed, conditional)
missing module named new - imported by reportlab.lib.attrmap (delayed, conditional)
missing module named reportlab.platypus.XPreformatted - imported by reportlab.platypus (top-level), reportlab.graphics.charts.textlabels (top-level)
missing module named reportlab_mods - imported by reportlab (optional)
missing module named 'reportlab.local_rl_mods' - imported by reportlab (optional)
missing module named 'reportlab.lib.pyHnj' - imported by reportlab.lib.utils (delayed, optional)
missing module named pyfribidi2 - imported by reportlab.pdfgen.textobject (conditional, optional)
missing module named gui.barcode_device_scanner - imported by gui.sales (delayed, optional), gui.inventory (delayed, optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named '_typeshed.wsgi' - imported by werkzeug._internal (conditional), werkzeug.exceptions (conditional), werkzeug.http (conditional), werkzeug.wsgi (conditional), werkzeug.utils (conditional), werkzeug.wrappers.response (conditional), werkzeug.test (conditional), werkzeug.datastructures.headers (conditional), werkzeug.formparser (conditional), werkzeug.wrappers.request (conditional), werkzeug.serving (conditional), werkzeug.debug (conditional), werkzeug.middleware.shared_data (conditional)
missing module named colorama - imported by werkzeug._internal (delayed, optional)
missing module named 'watchdog.observers' - imported by werkzeug._reloader (delayed)
missing module named 'watchdog.events' - imported by werkzeug._reloader (delayed)
missing module named watchdog - imported by werkzeug._reloader (delayed)
missing module named Sybase - imported by sqlalchemy.dialects.sybase.pysybase (delayed)
missing module named pysqlite2 - imported by sqlalchemy.dialects.sqlite.pysqlite (delayed, conditional, optional)
missing module named pysqlcipher - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, conditional)
missing module named pysqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, conditional)
missing module named sqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, conditional, optional)
missing module named asyncpg - imported by sqlalchemy.dialects.postgresql.asyncpg (delayed)
missing module named postgresql - imported by sqlalchemy.dialects.postgresql.pypostgresql (delayed)
missing module named pgdb - imported by sqlalchemy.dialects.postgresql.pygresql (delayed)
missing module named psycopg2 - imported by sqlalchemy.dialects.postgresql.psycopg2 (delayed)
missing module named cx_Oracle - imported by sqlalchemy.dialects.oracle.cx_oracle (delayed)
missing module named asyncmy - imported by sqlalchemy.dialects.mysql.asyncmy (delayed)
missing module named pymysql - imported by sqlalchemy.dialects.mysql.aiomysql (delayed)
missing module named 'mysql.connector' - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed, conditional, optional)
missing module named mysql - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed)
missing module named 'mx.ODBC' - imported by sqlalchemy.connectors.mxodbc (delayed, conditional)
missing module named mx - imported by sqlalchemy.connectors.mxodbc (delayed, conditional)
