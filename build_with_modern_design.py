#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء البرنامج مع التصميمات العصرية المحدثة
"""

import os
import sys
import shutil
import subprocess
import json
from datetime import datetime


def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 80)
    print("🚀 بناء البرنامج مع التصميمات العصرية المحدثة")
    print("=" * 80)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🏢 المطور: Sico Company")
    print("🎨 التصميم: عصري ومتطور")
    print("=" * 80)


def verify_modern_files():
    """التحقق من وجود الملفات العصرية"""
    print("\n🔍 التحقق من الملفات العصرية...")
    
    required_files = [
        'gui/modern_login.py',
        'assets/company_logo.png',
        'main.py'
    ]
    
    all_good = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - غير موجود")
            all_good = False
    
    # فحص محتوى main.py للتأكد من استخدام التصميم الجديد
    if os.path.exists('main.py'):
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'from gui.modern_login import LoginDialog' in content:
            print("✅ main.py يستخدم التصميم العصري")
        else:
            print("⚠️ main.py لا يستخدم التصميم العصري")
            all_good = False
    
    return all_good


def check_requirements():
    """فحص المتطلبات"""
    print("\n🔍 فحص المتطلبات...")
    
    required_modules = [
        'PyQt5',
        'sqlalchemy',
        'pyperclip',
        'Pillow'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module.lower().replace('-', '_'))
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - غير مثبت")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing_modules)}")
        return False
    
    print("✅ جميع المتطلبات متوفرة!")
    return True


def create_version_info():
    """إنشاء معلومات الإصدار"""
    print("\n📋 إنشاء معلومات الإصدار...")
    
    version_info = {
        "version": "2.1.0",
        "build_date": datetime.now().isoformat(),
        "main_file": "main.py",
        "design": "modern",
        "description": "النسخة العصرية مع تصميمات محدثة وأيقونات الشركة المطورة",
        "features": [
            "✅ تصميم تسجيل دخول عصري ومتطور",
            "✅ لوجو وأيقونات الشركة المطورة (Sico Company)",
            "✅ واجهة مستخدم محسنة وجذابة",
            "✅ إصلاح مشكلة عدم الدخول للبرنامج",
            "✅ أيقونة التطبيق محدثة في شريط المهام",
            "✅ جميع الإصلاحات السابقة مطبقة",
            "✅ تجربة مستخدم متطورة ومهنية"
        ],
        "design_updates": [
            "تصميم تسجيل دخول بجانبين (معلومات الشركة + نموذج الدخول)",
            "ألوان عصرية متدرجة وجذابة",
            "لوجو الشركة المطورة مع رسوم بيانية",
            "أيقونات وتأثيرات بصرية محسنة",
            "تخطيط متجاوب ومتوازن"
        ]
    }
    
    with open("version_info.json", "w", encoding="utf-8") as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)
    
    print("✅ تم إنشاء ملف معلومات الإصدار")


def build_executable():
    """بناء الملف التنفيذي"""
    print("\n🔨 بناء الملف التنفيذي مع التصميمات العصرية...")
    
    try:
        # التحقق من وجود PyInstaller
        subprocess.run([sys.executable, "-c", "import PyInstaller"], check=True)
        print("✅ PyInstaller متوفر")
    except subprocess.CalledProcessError:
        print("❌ PyInstaller غير مثبت")
        print("💡 قم بتثبيته: pip install pyinstaller")
        return False
    
    # بناء البرنامج مع الأصول الجديدة
    build_command = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed", 
        "--name", "نظام_المحاسبة_العصري_v2_1_عصري",
        "--distpath", "dist_modern",
        "--workpath", "build_temp",
        "--specpath", ".",
        "--add-data", "assets;assets",  # إضافة مجلد الأصول
        "main.py"
    ]
    
    # إضافة الأيقونة الجديدة
    if os.path.exists("assets/company_logo.png"):
        build_command.extend(["--icon", "assets/company_logo.png"])
    elif os.path.exists("assets/company_icon.png"):
        build_command.extend(["--icon", "assets/company_icon.png"])
    
    print("🔨 جاري البناء مع التصميمات العصرية...")
    print(f"📁 الأمر: {' '.join(build_command)}")
    
    try:
        result = subprocess.run(build_command, check=True, capture_output=True, text=True)
        print("✅ تم بناء الملف التنفيذي بنجاح مع التصميمات العصرية!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في البناء: {e}")
        if e.stderr:
            print(f"تفاصيل الخطأ: {e.stderr}")
        return False


def create_modern_distribution():
    """إنشاء حزمة التوزيع العصرية"""
    print("\n📦 إنشاء حزمة التوزيع العصرية...")
    
    dist_folder = "نظام_المحاسبة_العصري_v2_1_عصري_نهائي"
    
    # إنشاء مجلد التوزيع
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    os.makedirs(dist_folder)
    
    # نسخ الملف التنفيذي
    exe_file = "dist_modern/نظام_المحاسبة_العصري_v2_1_عصري.exe"
    if os.path.exists(exe_file):
        shutil.copy2(exe_file, os.path.join(dist_folder, "نظام_المحاسبة_العصري_v2_1.exe"))
        print("✅ نسخ الملف التنفيذي العصري")
    else:
        print("❌ لم يتم العثور على الملف التنفيذي")
        return False
    
    # إنشاء ملف تشغيل عصري
    batch_content = '''@echo off
chcp 65001 > nul
title نظام المحاسبة العصري v2.1 - التصميم العصري
echo.
echo ========================================
echo    🏢 نظام المحاسبة العصري v2.1
echo    🎨 التصميم العصري المحدث
echo    💼 Sico Company
echo ========================================
echo.
echo 🚀 جاري تشغيل البرنامج...
echo.

if exist "نظام_المحاسبة_العصري_v2_1.exe" (
    start "" "نظام_المحاسبة_العصري_v2_1.exe"
    echo ✅ تم تشغيل البرنامج بنجاح
    echo.
    echo 🎨 الميزات الجديدة في v2.1:
    echo    • تصميم تسجيل دخول عصري ومتطور
    echo    • لوجو وأيقونات الشركة المطورة
    echo    • واجهة مستخدم محسنة وجذابة
    echo    • إصلاح مشكلة عدم الدخول للبرنامج
    echo    • أيقونة التطبيق محدثة
    echo.
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 💡 تأكد من وجود الملف في نفس المجلد
    pause
)

timeout /t 5 > nul
'''
    
    with open(os.path.join(dist_folder, "تشغيل_البرنامج_العصري.bat"), "w", encoding="utf-8") as f:
        f.write(batch_content)
    print("✅ إنشاء ملف التشغيل العصري")
    
    # نسخ الملفات المساعدة
    files_to_copy = [
        ("version_info.json", "معلومات_الإصدار.json"),
        ("main.py", "main_source.py")
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, os.path.join(dist_folder, dst))
            print(f"✅ نسخ {dst}")
    
    # إنشاء دليل المستخدم العصري
    readme_content = f"""# 🏢 نظام المحاسبة العصري - الإصدار 2.1

## 🎨 التصميم العصري الجديد

### ✨ الميزات الجديدة في v2.1:
🎨 **تصميم تسجيل دخول عصري ومتطور**
🏢 **لوجو وأيقونات الشركة المطورة (Sico Company)**
💫 **واجهة مستخدم محسنة وجذابة**
🔧 **إصلاح مشكلة عدم الدخول للبرنامج**
🖼️ **أيقونة التطبيق محدثة في شريط المهام**

### 🚀 طريقة التشغيل:
1. انقر نقراً مزدوجاً على "تشغيل_البرنامج_العصري.bat"
2. أو انقر نقراً مزدوجاً على "نظام_المحاسبة_العصري_v2_1.exe"

### 🎯 التحسينات المطبقة:
✅ تصميم تسجيل دخول بجانبين (معلومات الشركة + نموذج الدخول)
✅ ألوان عصرية متدرجة وجذابة
✅ لوجو الشركة المطورة مع رسوم بيانية
✅ أيقونات وتأثيرات بصرية محسنة
✅ تخطيط متجاوب ومتوازن

### 📞 الدعم الفني:
📧 البريد الإلكتروني: <EMAIL>
🏢 الشركة: Sico Company

## 📅 تاريخ الإصدار
{datetime.now().strftime("%Y-%m-%d")}

© 2024 Sico Company - التصميم العصري
"""
    
    with open(os.path.join(dist_folder, "دليل_المستخدم_العصري.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    print("✅ إنشاء دليل المستخدم العصري")
    
    print(f"✅ تم إنشاء حزمة التوزيع العصرية: {dist_folder}")
    return dist_folder


def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("\n🧹 تنظيف الملفات المؤقتة...")
    
    temp_folders = ["build_temp", "dist_modern"]
    temp_files = ["*.spec"]
    
    for folder in temp_folders:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"🗑️ حذف {folder}")
    
    print("✅ تم التنظيف")


def main():
    """الدالة الرئيسية للبناء العصري"""
    print_header()
    
    # التحقق من الملفات العصرية
    if not verify_modern_files():
        print("\n❌ بعض الملفات العصرية مفقودة")
        return False
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        return False
    
    # إنشاء معلومات الإصدار
    create_version_info()
    
    # بناء الملف التنفيذي
    if not build_executable():
        print("\n❌ فشل في بناء الملف التنفيذي")
        return False
    
    # إنشاء حزمة التوزيع
    dist_folder = create_modern_distribution()
    
    if not dist_folder:
        print("\n❌ فشل في إنشاء حزمة التوزيع")
        return False
    
    # تنظيف الملفات المؤقتة
    cleanup()
    
    # النتيجة النهائية
    print("\n" + "=" * 80)
    print("🎉 تم بناء البرنامج العصري بنجاح!")
    print("=" * 80)
    print(f"📁 مجلد التوزيع: {dist_folder}")
    print("📋 الملفات المتضمنة:")
    print("   • نظام_المحاسبة_العصري_v2_1.exe - الملف التنفيذي العصري")
    print("   • تشغيل_البرنامج_العصري.bat - ملف التشغيل")
    print("   • دليل_المستخدم_العصري.txt - دليل الاستخدام")
    print("   • معلومات_الإصدار.json - تفاصيل الإصدار")
    print("\n🎨 التحسينات المطبقة:")
    print("   • تصميم تسجيل دخول عصري ومتطور")
    print("   • لوجو وأيقونات الشركة المطورة")
    print("   • إصلاح مشكلة عدم الدخول للبرنامج")
    print("   • أيقونة التطبيق محدثة")
    print("\n🚀 البرنامج العصري جاهز للاستخدام!")
    print("=" * 80)
    
    return True


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ تم البناء العصري بنجاح!")
    else:
        print("\n❌ فشل في البناء العصري!")
    
    input("\nاضغط Enter للخروج...")
