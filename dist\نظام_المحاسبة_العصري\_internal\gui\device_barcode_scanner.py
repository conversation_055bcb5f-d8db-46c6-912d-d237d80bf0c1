#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة مسح الباركود بالجهاز المخصص
تدعم أجهزة مسح الباركود المتصلة بـ USB أو Bluetooth
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QTextEdit, QFrame,
                             QMessageBox, QProgressBar, QCheckBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QIcon
import time
import threading

class DeviceBarcodeScannerDialog(QDialog):
    """نافذة مسح الباركود بالجهاز المخصص"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("مسح الباركود بالجهاز")
        self.setModal(True)

        # تعيين الحجم المطلوب الجديد (طولي)
        self.setMinimumSize(650, 1200)  # حد أدنى مناسب للشكل الطولي
        self.resize(750, 1400)  # الحجم الجديد المطلوب 750×1400 (طولي)

        # إضافة أزرار التحكم في النافذة (تكبير/تصغير)
        self.setWindowFlags(Qt.Dialog | Qt.WindowMinMaxButtonsHint | Qt.WindowCloseButtonHint)
        
        # متغيرات
        self.scanned_code = ""
        self.is_scanning = False
        self.auto_close = True
        
        self.setup_ui()
        self.setup_device_listener()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # العنوان
        title = QLabel("🔍 مسح الباركود بالجهاز")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2C3E50;
                padding: 20px;
                text-align: center;
                background-color: #ECF0F1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # منطقة التعليمات
        instructions_frame = QFrame()
        instructions_frame.setStyleSheet("""
            QFrame {
                background-color: #E8F6F3;
                border: 2px solid #1ABC9C;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        instructions_layout = QVBoxLayout()
        instructions_frame.setLayout(instructions_layout)
        
        instructions_title = QLabel("📋 تعليمات الاستخدام:")
        instructions_title.setStyleSheet("font-size: 20px; font-weight: bold; color: #16A085; padding: 8px;")
        instructions_layout.addWidget(instructions_title)

        instructions_text = QLabel("""
🔌 تأكد من توصيل جهاز مسح الباركود بالكمبيوتر

⚡ اضغط على زر "بدء المسح" أو اضغط F2

🎯 وجه الجهاز نحو الباركود واضغط الزناد

✅ سيتم إدخال الباركود تلقائياً في الحقل

✍️ يمكنك أيضاً كتابة الباركود يدوياً في الحقل أدناه
        """)
        instructions_text.setStyleSheet("""
            font-size: 18px;
            color: #2C3E50;
            line-height: 2.2;
            padding: 15px;
            background-color: #F8F9FA;
            border-radius: 8px;
            min-height: 180px;
        """)
        instructions_layout.addWidget(instructions_text)
        
        layout.addWidget(instructions_frame)
        
        # حالة الجهاز
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #FEF9E7;
                border: 2px solid #F39C12;
                border-radius: 10px;
                padding: 10px;
                margin: 10px 0;
            }
        """)
        status_layout = QHBoxLayout()
        status_frame.setLayout(status_layout)
        
        self.status_label = QLabel("⏳ في انتظار توصيل الجهاز...")
        self.status_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #D68910; padding: 8px;")
        status_layout.addWidget(self.status_label)

        layout.addWidget(status_frame)

        # حقل إدخال الباركود
        input_frame = QFrame()
        input_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 2px solid #BDC3C7;
                border-radius: 10px;
                padding: 20px;
                margin: 15px 0;
            }
        """)
        input_layout = QVBoxLayout()
        input_frame.setLayout(input_layout)

        input_label = QLabel("📝 الباركود المسحوب:")
        input_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #2C3E50; margin-bottom: 15px; padding: 5px;")
        input_layout.addWidget(input_label)

        self.barcode_input = QLineEdit()
        self.barcode_input.setStyleSheet("""
            QLineEdit {
                padding: 20px;
                font-size: 22px;
                font-family: 'Courier New', monospace;
                border: 3px solid #3498DB;
                border-radius: 10px;
                background-color: white;
                text-align: center;
                min-height: 60px;
            }
            QLineEdit:focus {
                border-color: #2980B9;
                background-color: #EBF5FB;
            }
        """)
        self.barcode_input.setPlaceholderText("سيظهر الباركود هنا تلقائياً...")
        self.barcode_input.textChanged.connect(self.on_barcode_changed)
        input_layout.addWidget(self.barcode_input)
        
        layout.addWidget(input_frame)
        
        # خيارات المسح
        options_frame = QFrame()
        options_frame.setStyleSheet("""
            QFrame {
                background-color: #F4F6F7;
                border: 1px solid #D5DBDB;
                border-radius: 8px;
                padding: 10px;
                margin: 10px 0;
            }
        """)
        options_layout = QHBoxLayout()
        options_frame.setLayout(options_layout)
        
        self.auto_close_checkbox = QCheckBox("إغلاق النافذة تلقائياً بعد المسح")
        self.auto_close_checkbox.setChecked(True)
        self.auto_close_checkbox.setStyleSheet("font-size: 18px; color: #2C3E50; padding: 8px;")
        self.auto_close_checkbox.stateChanged.connect(self.on_auto_close_changed)
        options_layout.addWidget(self.auto_close_checkbox)

        layout.addWidget(options_frame)

        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout()
        buttons_frame.setLayout(buttons_layout)

        # زر بدء المسح
        self.scan_btn = QPushButton("🔍 بدء المسح (F2)")
        self.scan_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 18px 35px;
                border: none;
                border-radius: 10px;
                min-width: 180px;
                min-height: 55px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1E8449;
            }
        """)
        self.scan_btn.clicked.connect(self.start_scanning)
        buttons_layout.addWidget(self.scan_btn)
        
        # زر إيقاف المسح
        self.stop_btn = QPushButton("⏹️ إيقاف المسح")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 18px 35px;
                border: none;
                border-radius: 10px;
                min-width: 180px;
                min-height: 55px;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_scanning)
        self.stop_btn.setEnabled(False)
        buttons_layout.addWidget(self.stop_btn)

        buttons_layout.addStretch()

        # زر موافق
        self.ok_btn = QPushButton("✅ موافق")
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 18px 35px;
                border: none;
                border-radius: 10px;
                min-width: 150px;
                min-height: 55px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        self.ok_btn.clicked.connect(self.accept_barcode)
        self.ok_btn.setEnabled(False)
        buttons_layout.addWidget(self.ok_btn)

        # زر إلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 18px 35px;
                border: none;
                border-radius: 10px;
                min-width: 150px;
                min-height: 55px;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addWidget(buttons_frame)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                color: #2C3E50;
            }
            QProgressBar::chunk {
                background-color: #3498DB;
                border-radius: 3px;
            }
        """)
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # تركيز على حقل الإدخال
        self.barcode_input.setFocus()
        
    def setup_device_listener(self):
        """إعداد مستمع الجهاز"""
        # محاكاة حالة الجهاز
        QTimer.singleShot(1000, self.simulate_device_ready)
        
    def simulate_device_ready(self):
        """محاكاة جاهزية الجهاز"""
        self.status_label.setText("✅ الجهاز جاهز للمسح")
        self.status_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #27AE60; padding: 8px;")

    def start_scanning(self):
        """بدء عملية المسح"""
        self.is_scanning = True
        self.scan_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.status_label.setText("🔍 جاري المسح... وجه الجهاز نحو الباركود")
        self.status_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #3498DB; padding: 8px;")

        # تركيز على حقل الإدخال لاستقبال البيانات من الجهاز
        self.barcode_input.setFocus()
        self.barcode_input.clear()

    def stop_scanning(self):
        """إيقاف عملية المسح"""
        self.is_scanning = False
        self.scan_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("⏹️ تم إيقاف المسح")
        self.status_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #E74C3C; padding: 8px;")
        
    def on_barcode_changed(self, text):
        """عند تغيير نص الباركود"""
        if text.strip():
            self.ok_btn.setEnabled(True)
            if self.is_scanning and len(text) >= 8:  # افتراض أن الباركود 8 أرقام على الأقل
                self.scanned_code = text.strip()
                self.status_label.setText(f"✅ تم مسح الباركود: {self.scanned_code}")
                self.status_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #27AE60; padding: 8px;")
                
                if self.auto_close:
                    QTimer.singleShot(1000, self.accept_barcode)  # إغلاق تلقائي بعد ثانية
        else:
            self.ok_btn.setEnabled(False)
            
    def on_auto_close_changed(self, state):
        """عند تغيير خيار الإغلاق التلقائي"""
        self.auto_close = state == Qt.Checked
        
    def accept_barcode(self):
        """قبول الباركود والإغلاق"""
        if self.barcode_input.text().strip():
            self.scanned_code = self.barcode_input.text().strip()
            self.accept()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال أو مسح باركود صحيح")
            
    def keyPressEvent(self, event):
        """معالجة ضغطات المفاتيح"""
        if event.key() == Qt.Key_F2:
            if not self.is_scanning:
                self.start_scanning()
            else:
                self.stop_scanning()
        elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            if self.barcode_input.text().strip():
                self.accept_barcode()
        else:
            super().keyPressEvent(event)
