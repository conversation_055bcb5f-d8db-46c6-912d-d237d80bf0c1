# نظام الطباعة الجديد 2025

## نظرة عامة
تم إعادة تصميم نظام طباعة الفواتير بالكامل ليدعم نوعين من الطابعات مع تخطيط محسن وفق المتطلبات الجديدة.

## المميزات الجديدة

### 1. اختيار نوع الطابعة
- **طابعة عادية (A4)**: تخطيط مقسم إلى أرباع مع إمكانية التوسع لصفحات إضافية
- **طابعة رول (ورق صغير)**: تخطيط مضغوط مناسب للورق الصغير (80مم)

### 2. تخطيط A4 المحسن
#### الربع الأول: معلومات الشركة واللوجو
- معلومات الشركة على الجهة اليمنى (اسم الشركة، العنوان، الهاتف، البريد الإلكتروني، الرقم الضريبي)
- لوجو الشركة على الجهة اليسرى بحجم مساوي لمعلومات الشركة

#### الربع الثاني: معلومات العميل والفاتورة
- بيانات العميل (الاسم، الهاتف، العنوان)
- رقم الفاتورة والتاريخ

#### الربع الثالث: تفاصيل المنتجات
- جدول المنتجات مع الأعمدة: المنتج، الكمية، الوحدة، السعر، الإجمالي
- يدعم حتى 8 منتجات في الصفحة الأولى

#### الربع الرابع: ملخص الأسعار
- المجموع الفرعي
- الخصم (إن وجد)
- الإجمالي النهائي
- المدفوع
- المتبقي (إن وجد)

### 3. الصفحات الإضافية
- إذا كانت المنتجات أكثر من 8، يتم إنشاء صفحة إضافية للمنتجات المتبقية
- الصفحة الإضافية تحتوي على جدول المنتجات فقط مع عنوان واضح

### 4. تخطيط الرول
- تصميم مضغوط مناسب لعرض 80مم
- ترويسة مبسطة مع لوجو الشركة
- معلومات العميل والفاتورة
- جدول المنتجات مع 4 أعمدة فقط
- ملخص الأسعار

### 5. وظائف الطباعة
- **معاينة قبل الطباعة**: عرض الفاتورة قبل الطباعة مع إمكانية التكبير والتصغير
- **حفظ كـ PDF**: حفظ الفاتورة كملف PDF مع إمكانية فتحه مباشرة
- **طباعة مباشرة**: إرسال الفاتورة مباشرة للطابعة المختارة

## الملفات المحدثة

### ملفات جديدة/محدثة:
- `utils/quarter_invoice_printer.py`: النظام الجديد الكامل
- `gui/invoices_view.py`: محدث لاستخدام النظام الجديد
- `gui/sales.py`: محدث لاستخدام النظام الجديد
- `gui/purchases.py`: محدث لاستخدام النظام الجديد

### ملفات محذوفة:
- `utils/invoice_printer.py`: النظام القديم
- `utils/simple_invoice_printer.py`: النظام القديم المبسط

## كيفية الاستخدام

### من الكود:
```python
from utils.quarter_invoice_printer import show_new_print_dialog

# عرض نافذة الطباعة
show_new_print_dialog(engine, invoice_id, parent_widget)
```

### من واجهة المستخدم:
1. اختر الفاتورة المراد طباعتها
2. اضغط على زر "طباعة"
3. اختر نوع الطابعة (A4 أو رول)
4. اختر العملية المطلوبة:
   - معاينة قبل الطباعة
   - حفظ كـ PDF
   - طباعة مباشرة

## التحسينات التقنية

### الأداء:
- تحسين سرعة تحميل البيانات
- تحسين عرض HTML للطباعة
- دعم أفضل للخطوط العربية

### التوافق:
- دعم جميع أحجام الورق
- توافق مع طابعات مختلفة
- دعم معاينة الطباعة في جميع المتصفحات

### الأمان:
- التحقق من صحة البيانات قبل الطباعة
- معالجة أفضل للأخطاء
- رسائل خطأ واضحة للمستخدم

## المتطلبات التقنية
- PyQt5
- Python 3.7+
- إعدادات الشركة محفوظة في قاعدة البيانات
- دعم الطابعات المحلية

## الدعم والصيانة
النظام مصمم ليكون قابل للصيانة والتطوير مع:
- كود منظم ومعلق
- فصل واضح بين المنطق والعرض
- سهولة إضافة أنواع طابعات جديدة
- دعم التخصيص المستقبلي
