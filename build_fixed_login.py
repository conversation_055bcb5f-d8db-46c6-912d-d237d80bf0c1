#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء سريع مع إصلاح مشكلة تسجيل الدخول
"""

import os
import sys
import subprocess
import shutil

def main():
    print("🚀 بناء سريع مع إصلاح مشكلة تسجيل الدخول...")
    print("=" * 60)
    
    # أمر البناء السريع
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name", "نظام_المحاسبة_العصري_v2_مُصلح",
        "main.py"
    ]
    
    print("🔨 بدء البناء...")
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ تم البناء بنجاح!")
        
        # التحقق من الملف الناتج
        exe_path = "dist/نظام_المحاسبة_العصري_v2_مُصلح.exe"
        if os.path.exists(exe_path):
            print(f"✅ الملف التنفيذي: {exe_path}")
            
            # نسخ إلى المجلد الحالي
            shutil.copy2(exe_path, ".")
            print("✅ تم نسخ الملف للمجلد الحالي")
            
            # إنشاء ملف تشغيل سريع
            with open("تشغيل_البرنامج_المُصلح.bat", "w", encoding="utf-8") as f:
                f.write('''@echo off
chcp 65001 > nul
title نظام المحاسبة العصري v2 - مُصلح
echo.
echo ========================================
echo    🏢 نظام المحاسبة العصري v2
echo    🔧 الإصدار المُصلح
echo ========================================
echo.
echo 🚀 جاري تشغيل البرنامج...
echo.

if exist "نظام_المحاسبة_العصري_v2_مُصلح.exe" (
    start "" "نظام_المحاسبة_العصري_v2_مُصلح.exe"
    echo ✅ تم تشغيل البرنامج بنجاح
    echo.
    echo 🔐 بيانات تسجيل الدخول:
    echo    • اسم المستخدم: sicoo
    echo    • كلمة المرور: sicoo123
    echo.
    echo ✅ الإصلاحات المطبقة:
    echo    • إصلاح مشكلة عدم فتح البرنامج بعد تسجيل الدخول
    echo    • لوجو الشركة المطورة (Sico Company)
    echo    • تحسين واجهة تسجيل الدخول
    echo.
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    pause
)

timeout /t 5 > nul
''')
            
            print("✅ تم إنشاء ملف التشغيل السريع")
            
            print("\n🎉 البرنامج المُصلح جاهز!")
            print("📁 الملفات الجاهزة:")
            print("   • نظام_المحاسبة_العصري_v2_مُصلح.exe")
            print("   • تشغيل_البرنامج_المُصلح.bat")
            print("\n🔐 بيانات تسجيل الدخول:")
            print("   • اسم المستخدم: sicoo")
            print("   • كلمة المرور: sicoo123")
            print("\n✅ الإصلاحات المطبقة:")
            print("   • إصلاح مشكلة عدم فتح البرنامج بعد تسجيل الدخول")
            print("   • لوجو الشركة المطورة")
            print("   • تحسين واجهة تسجيل الدخول")
            
            return True
        else:
            print("❌ لم يتم العثور على الملف التنفيذي")
            return False
            
    except Exception as e:
        print(f"❌ فشل البناء: {e}")
        return False

if __name__ == "__main__":
    if main():
        print("\n🎉 البناء مكتمل بنجاح!")
    else:
        print("\n❌ فشل البناء!")
    
    input("اضغط Enter للخروج...")
