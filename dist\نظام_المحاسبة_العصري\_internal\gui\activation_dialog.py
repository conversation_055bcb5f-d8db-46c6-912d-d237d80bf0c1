from PyQt5.QtWidgets import (<PERSON><PERSON><PERSON>og, QVBoxLayout, QHBox<PERSON>ayout, QLabel, QLineEdit, QPushButton, QMessageBox, QApplication)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon
import pyperclip
from license_manager import LicenseManager
from main import resource_path
import os

class ActivationDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.license_manager = LicenseManager()
        self.setWindowTitle("تفعيل البرنامج - معلومات هامة")
        self.setWindowIcon(QIcon(resource_path(os.path.join('assets', 'icons.ico'))))
        self.setMinimumWidth(550)
        self.setLayoutDirection(Qt.RightToLeft)
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContents<PERSON>argins(30, 30, 30, 30)

        # تطبيق تصميم حديث
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-radius: 15px;
            }
            QLabel {
                color: #2c3e50;
            }
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                background: white;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background: #f8f9fa;
            }
            QPushButton {
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                transform: translateY(-2px);
            }
        """)

        # العنوان الرئيسي
        title_label = QLabel("🎉 مرحباً بك في نظام المحاسبة العصري!")
        title_label.setFont(QFont("Arial", 20, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            color: #2c3e50;
            margin: 10px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 2px solid #3498db;
        """)
        main_layout.addWidget(title_label)

        # رسالة ترحيبية
        welcome_label = QLabel("تم تثبيت البرنامج بنجاح! 🎊")
        welcome_label.setFont(QFont("Arial", 16, QFont.Bold))
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("color: #27ae60; margin: 10px 0;")
        main_layout.addWidget(welcome_label)

        # تعليمات التفعيل
        instructions_label = QLabel(
            "للحصول على ترخيص كامل أو الدعم الفني، يرجى إرسال المعلومات التالية إلى المطور:"
        )
        instructions_label.setFont(QFont("Arial", 12))
        instructions_label.setAlignment(Qt.AlignCenter)
        instructions_label.setWordWrap(True)
        instructions_label.setStyleSheet("color: #7f8c8d; margin: 10px 0; line-height: 1.5;")
        main_layout.addWidget(instructions_label)

        # إطار معلومات التفعيل
        info_frame = QLabel()
        info_frame.setStyleSheet("""
            background: white;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
        """)
        info_frame.setFixedHeight(200)

        info_layout = QVBoxLayout(info_frame)
        info_layout.setSpacing(15)

        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()

        # عنوان المعلومات
        info_title = QLabel("📋 معلومات التفعيل")
        info_title.setFont(QFont("Arial", 14, QFont.Bold))
        info_title.setAlignment(Qt.AlignCenter)
        info_title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        info_layout.addWidget(info_title)

        self.customer_code_edit = self.create_info_field("🔑 كود العميل:", customer_code)
        self.machine_id_edit = self.create_info_field("💻 رقم الجهاز:", machine_id)

        info_layout.addWidget(self.customer_code_edit['label'])
        info_layout.addWidget(self.customer_code_edit['input'])
        info_layout.addWidget(self.machine_id_edit['label'])
        info_layout.addWidget(self.machine_id_edit['input'])

        main_layout.addWidget(info_frame)

        # معلومات الفترة التجريبية
        trial_info = QLabel("🕐 ستحصل على فترة تجريبية مجانية لمدة 30 يوماً")
        trial_info.setFont(QFont("Arial", 12, QFont.Bold))
        trial_info.setAlignment(Qt.AlignCenter)
        trial_info.setStyleSheet("""
            color: #f39c12;
            background: #fef9e7;
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
        """)
        main_layout.addWidget(trial_info)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        self.copy_button = QPushButton("📋 نسخ المعلومات")
        self.copy_button.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3498db, stop:1 #2980b9);
            color: white;
        """)
        self.copy_button.clicked.connect(self.copy_info)

        self.continue_button = QPushButton("🚀 بدء الاستخدام")
        self.continue_button.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #27ae60, stop:1 #229954);
            color: white;
        """)
        self.continue_button.clicked.connect(self.accept)

        buttons_layout.addWidget(self.copy_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.continue_button)
        main_layout.addLayout(buttons_layout)

    def create_info_field(self, label_text, value_text):
        label = QLabel(label_text)
        label.setFont(QFont("Arial", 12, QFont.Bold))
        
        line_edit = QLineEdit(value_text)
        line_edit.setFont(QFont("Arial", 12))
        line_edit.setReadOnly(True)
        line_edit.setAlignment(Qt.AlignCenter)
        line_edit.setStyleSheet("padding: 5px; border: 1px solid #ccc; border-radius: 5px;")

        return {"label": label, "input": line_edit}

    def copy_info(self):
        customer_code = self.customer_code_edit['input'].text()
        machine_id = self.machine_id_edit['input'].text()

        # تنسيق المعلومات بشكل احترافي
        info_to_copy = f"""
=== معلومات تفعيل نظام المحاسبة العصري ===

🔑 كود العميل: {customer_code}
💻 رقم الجهاز: {machine_id}

📧 يرجى إرسال هذه المعلومات للحصول على ترخيص كامل
📞 للدعم الفني: اتصل بالمطور

© 2024 Sico Company - نظام المحاسبة العصري
        """.strip()

        try:
            pyperclip.copy(info_to_copy)
            QMessageBox.information(
                self,
                "✅ تم النسخ بنجاح",
                "تم نسخ معلومات التفعيل إلى الحافظة بنجاح!\n\n"
                "يمكنك الآن لصقها في رسالة إلى المطور للحصول على الترخيص الكامل."
            )
        except Exception as e:
            # عرض المعلومات في نافذة منفصلة إذا فشل النسخ
            QMessageBox.information(
                self,
                "معلومات التفعيل",
                f"يرجى نسخ المعلومات التالية يدوياً:\n\n{info_to_copy}"
            )
            print(f"Clipboard error: {e}")

if __name__ == '__main__':
    import sys
    app = QApplication(sys.argv)
    dialog = ActivationDialog()
    sys.exit(dialog.exec_())