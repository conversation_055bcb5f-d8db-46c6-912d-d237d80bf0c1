#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عرض معلومات الترخيص ورقم الجهاز
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def test_license_manager_import():
    """اختبار استيراد مدير الترخيص"""
    print("🔍 اختبار استيراد مدير الترخيص...")
    
    try:
        from license_manager import LicenseManager
        print("✅ تم استيراد LicenseManager بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد LicenseManager: {e}")
        return False

def test_license_manager_functions():
    """اختبار دوال مدير الترخيص"""
    print("\n🔍 اختبار دوال مدير الترخيص...")
    
    try:
        from license_manager import LicenseManager
        
        license_manager = LicenseManager()
        
        # اختبار الحصول على كود العميل
        customer_code = license_manager.get_customer_code()
        if customer_code:
            print(f"✅ كود العميل: {customer_code}")
        else:
            print("❌ فشل في الحصول على كود العميل")
            return False
        
        # اختبار الحصول على رقم الجهاز
        machine_id = license_manager.get_machine_id()
        if machine_id:
            print(f"✅ رقم الجهاز: {machine_id}")
        else:
            print("❌ فشل في الحصول على رقم الجهاز")
            return False
        
        # اختبار فحص الترخيص
        status = license_manager.check_license()
        if status:
            print(f"✅ حالة الترخيص: {status}")
        else:
            print("❌ فشل في فحص الترخيص")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال مدير الترخيص: {e}")
        return False

def test_main_window_license_menu():
    """اختبار قائمة معلومات الترخيص في الواجهة الرئيسية"""
    print("\n🔍 اختبار قائمة معلومات الترخيص...")
    
    try:
        # قراءة ملف الواجهة الرئيسية
        with open('gui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن إضافة معلومات الترخيص في القائمة
        if 'معلومات الترخيص' in content:
            print("✅ تم إضافة 'معلومات الترخيص' في القائمة")
        else:
            print("❌ لم يتم إضافة 'معلومات الترخيص' في القائمة")
            return False
        
        # البحث عن دالة show_license_info
        if 'def show_license_info(self):' in content:
            print("✅ تم إضافة دالة show_license_info")
        else:
            print("❌ لم يتم إضافة دالة show_license_info")
            return False
        
        # البحث عن زر معلومات الترخيص في الأزرار الرئيسية
        if '🔑 معلومات الترخيص' in content:
            print("✅ تم إضافة زر معلومات الترخيص في الصفحة الرئيسية")
        else:
            print("❌ لم يتم إضافة زر معلومات الترخيص في الصفحة الرئيسية")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قائمة الترخيص: {e}")
        return False

def test_pyperclip_import():
    """اختبار استيراد مكتبة النسخ"""
    print("\n🔍 اختبار استيراد مكتبة النسخ...")
    
    try:
        import pyperclip
        print("✅ تم استيراد pyperclip بنجاح")
        
        # اختبار النسخ
        test_text = "اختبار النسخ"
        pyperclip.copy(test_text)
        copied_text = pyperclip.paste()
        
        if copied_text == test_text:
            print("✅ النسخ واللصق يعملان بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في النسخ واللصق")
            return False
        
    except Exception as e:
        print(f"⚠️ تحذير: مكتبة pyperclip غير متاحة - {e}")
        print("💡 سيتم عرض المعلومات في نافذة بدلاً من النسخ")
        return True  # ليس خطأ قاتل

def test_license_info_dialog_creation():
    """اختبار إنشاء نافذة معلومات الترخيص"""
    print("\n🔍 اختبار إنشاء نافذة معلومات الترخيص...")
    
    try:
        from sqlalchemy import create_engine
        from database.users import init_db
        
        # إنشاء قاعدة بيانات مؤقتة للاختبار
        engine = create_engine('sqlite:///test_license.db', echo=False)
        init_db(engine)
        
        app = QApplication(sys.argv)
        
        from gui.main_window import MainWindow
        
        # إنشاء مستخدم وهمي للاختبار
        class MockUser:
            def __init__(self):
                self.username = "test_user"
                self.full_name = "مستخدم تجريبي"
                self.roles = []
        
        user = MockUser()
        window = MainWindow(engine=engine, user=user)
        
        # اختبار وجود دالة معلومات الترخيص
        if hasattr(window, 'show_license_info'):
            print("✅ دالة show_license_info موجودة في MainWindow")
            
            # اختبار وجود دالة نسخ المعلومات
            if hasattr(window, 'copy_license_info'):
                print("✅ دالة copy_license_info موجودة")
            else:
                print("❌ دالة copy_license_info غير موجودة")
                return False
        else:
            print("❌ دالة show_license_info غير موجودة")
            return False
        
        # إغلاق النافذة
        window.close()
        app.quit()
        
        # حذف قاعدة البيانات المؤقتة
        if os.path.exists('test_license.db'):
            os.remove('test_license.db')
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء نافذة الترخيص: {e}")
        return False

def test_license_info_accessibility():
    """اختبار إمكانية الوصول لمعلومات الترخيص"""
    print("\n🔍 اختبار إمكانية الوصول لمعلومات الترخيص...")
    
    try:
        # قراءة ملف الواجهة الرئيسية
        with open('gui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        access_points = []
        
        # البحث عن نقاط الوصول المختلفة
        if 'license_info_action' in content:
            access_points.append("قائمة النظام")
        
        if '🔑 معلومات الترخيص' in content:
            access_points.append("الصفحة الرئيسية")
        
        if len(access_points) >= 2:
            print(f"✅ يمكن الوصول لمعلومات الترخيص من: {', '.join(access_points)}")
            return True
        elif len(access_points) == 1:
            print(f"⚠️ يمكن الوصول لمعلومات الترخيص من: {access_points[0]} فقط")
            return True
        else:
            print("❌ لا يمكن الوصول لمعلومات الترخيص")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إمكانية الوصول: {e}")
        return False

def test_code_syntax():
    """اختبار صحة الكود النحوية"""
    print("\n🔍 اختبار صحة الكود النحوية...")
    
    try:
        with open('gui/main_window.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # محاولة تجميع الكود للتحقق من صحته النحوية
        compile(code, 'gui/main_window.py', 'exec')
        print("✅ الكود صحيح نحوياً")
        return True
        
    except SyntaxError as e:
        print(f"❌ خطأ نحوي في الكود: {e}")
        return False
    except Exception as e:
        print(f"⚠️ تحذير في الكود: {e}")
        return True

def main():
    """تشغيل جميع اختبارات معلومات الترخيص"""
    print("🧪 بدء اختبار معلومات الترخيص...")
    print("=" * 60)
    
    tests = [
        ("صحة الكود النحوية", test_code_syntax),
        ("استيراد مدير الترخيص", test_license_manager_import),
        ("دوال مدير الترخيص", test_license_manager_functions),
        ("قائمة معلومات الترخيص", test_main_window_license_menu),
        ("مكتبة النسخ", test_pyperclip_import),
        ("إنشاء نافذة الترخيص", test_license_info_dialog_creation),
        ("إمكانية الوصول", test_license_info_accessibility)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبارات معلومات الترخيص:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 النتيجة النهائية: {passed}/{len(results)} اختبارات نجحت")
    
    if passed >= len(results) - 1:  # السماح بفشل اختبار واحد
        print("🎉 معلومات الترخيص تعمل بشكل صحيح!")
        print("✅ الميزات المتاحة:")
        print("   - عرض كود العميل ورقم الجهاز")
        print("   - عرض حالة الترخيص")
        print("   - نسخ المعلومات للحافظة")
        print("   - الوصول من قائمة النظام")
        print("   - الوصول من الصفحة الرئيسية")
        print("   - واجهة عصرية وسهلة الاستخدام")
    else:
        print("⚠️ بعض اختبارات معلومات الترخيص تحتاج مراجعة")

if __name__ == "__main__":
    main()
