#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء مبسط للتصميم العصري
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def main():
    print("🚀 بناء مبسط للتصميم العصري...")
    print("=" * 50)
    
    # التحقق من الملفات المطلوبة
    required_files = [
        'main.py',
        'gui/modern_login.py',
        'assets/company_logo.png'
    ]
    
    print("🔍 فحص الملفات المطلوبة...")
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - غير موجود")
            return False
    
    # أمر البناء المبسط
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name", "نظام_المحاسبة_العصري_v2_1_عصري",
        "main.py"
    ]
    
    print("\n🔨 بدء البناء العصري...")
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ تم البناء بنجاح!")
        
        # التحقق من الملف الناتج
        exe_path = "dist/نظام_المحاسبة_العصري_v2_1_عصري.exe"
        if os.path.exists(exe_path):
            print(f"✅ الملف التنفيذي: {exe_path}")
            
            # إنشاء مجلد التوزيع العصري
            dist_folder = "نظام_المحاسبة_العصري_v2_1_عصري_نهائي"
            if os.path.exists(dist_folder):
                shutil.rmtree(dist_folder)
            os.makedirs(dist_folder)
            
            # نسخ الملف التنفيذي
            shutil.copy2(exe_path, os.path.join(dist_folder, "نظام_المحاسبة_العصري_v2_1.exe"))
            print("✅ تم نسخ الملف التنفيذي")
            
            # إنشاء ملف تشغيل
            batch_content = '''@echo off
chcp 65001 > nul
title نظام المحاسبة العصري v2.1 - التصميم العصري
echo.
echo ========================================
echo    🏢 نظام المحاسبة العصري v2.1
echo    🎨 التصميم العصري المحدث
echo    💼 Sico Company
echo ========================================
echo.
echo 🚀 جاري تشغيل البرنامج...
echo.

if exist "نظام_المحاسبة_العصري_v2_1.exe" (
    start "" "نظام_المحاسبة_العصري_v2_1.exe"
    echo ✅ تم تشغيل البرنامج بنجاح
    echo.
    echo 🎨 الميزات الجديدة في v2.1:
    echo    • تصميم تسجيل دخول عصري ومتطور
    echo    • لوجو وأيقونات الشركة المطورة
    echo    • واجهة مستخدم محسنة وجذابة
    echo    • إصلاح مشكلة عدم الدخول للبرنامج
    echo    • أيقونة التطبيق محدثة
    echo.
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 💡 تأكد من وجود الملف في نفس المجلد
    pause
)

timeout /t 5 > nul
'''
            
            with open(os.path.join(dist_folder, "تشغيل_البرنامج_العصري.bat"), "w", encoding="utf-8") as f:
                f.write(batch_content)
            print("✅ تم إنشاء ملف التشغيل")
            
            # إنشاء دليل المستخدم
            readme_content = f"""# 🏢 نظام المحاسبة العصري - الإصدار 2.1

## 🎨 التصميم العصري الجديد

### ✨ الميزات الجديدة في v2.1:
🎨 **تصميم تسجيل دخول عصري ومتطور**
🏢 **لوجو وأيقونات الشركة المطورة (Sico Company)**
💫 **واجهة مستخدم محسنة وجذابة**
🔧 **إصلاح مشكلة عدم الدخول للبرنامج**
🖼️ **أيقونة التطبيق محدثة في شريط المهام**

### 🚀 طريقة التشغيل:
1. انقر نقراً مزدوجاً على "تشغيل_البرنامج_العصري.bat"
2. أو انقر نقراً مزدوجاً على "نظام_المحاسبة_العصري_v2_1.exe"

### 🔐 بيانات تسجيل الدخول الافتراضية:
- اسم المستخدم: sicoo
- كلمة المرور: sicoo123

### 📞 الدعم الفني:
📧 البريد الإلكتروني: <EMAIL>
🏢 الشركة: Sico Company

## 📅 تاريخ الإصدار
{datetime.now().strftime("%Y-%m-%d")}

© 2024 Sico Company - التصميم العصري
"""
            
            with open(os.path.join(dist_folder, "دليل_المستخدم_العصري.txt"), "w", encoding="utf-8") as f:
                f.write(readme_content)
            print("✅ تم إنشاء دليل المستخدم")
            
            print(f"\n🎉 تم إنشاء حزمة التوزيع العصرية: {dist_folder}")
            print("\n📋 الملفات المتضمنة:")
            print("   • نظام_المحاسبة_العصري_v2_1.exe")
            print("   • تشغيل_البرنامج_العصري.bat")
            print("   • دليل_المستخدم_العصري.txt")
            
            print("\n🎨 الإصلاحات المطبقة:")
            print("   ✅ تصميم تسجيل دخول عصري")
            print("   ✅ لوجو الشركة المطورة")
            print("   ✅ إصلاح مشكلة عدم الدخول")
            print("   ✅ أيقونة التطبيق محدثة")
            
            return True
        else:
            print("❌ لم يتم العثور على الملف التنفيذي")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل البناء: {e}")
        if e.stdout:
            print(f"المخرجات: {e.stdout}")
        if e.stderr:
            print(f"الأخطاء: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    if main():
        print("\n🎉 البناء العصري مكتمل!")
    else:
        print("\n❌ فشل البناء العصري!")
    
    input("اضغط Enter للخروج...")
