from PyQt5.QtWidgets import QApplication, QMessageBox, QDialog
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon
import sys
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from database.users import init_db
from database.models import clear_all_data

# استيراد نظام التراخيص
try:
    from license_manager import LicenseManager
    LICENSE_SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: نظام التراخيص غير متوفر - {e}")
    LICENSE_SYSTEM_AVAILABLE = False

def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.dirname(__file__), relative_path)

def is_first_ever_run():
    """Check if the program is being run for the very first time."""
    return not os.path.exists(resource_path('license.dat'))

def main():
    try:
        # تهيئة قاعدة البيانات أولاً
        db_path = resource_path('accounting.db')
        print(f"🔍 مسار قاعدة البيانات الفعلي: {db_path}")
        db_exists = os.path.exists(db_path)
        engine = create_engine(f'sqlite:///{db_path}', echo=False) # Changed to False for cleaner logs
        if not db_exists:
            print("📦 قاعدة البيانات غير موجودة، سيتم إنشاؤها الآن...")
            init_db(engine)
            print("✅ تم إنشاء قاعدة البيانات بنجاح!")
        else:
            print("✅ قاعدة البيانات موجودة بالفعل.")

        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين أيقونة البرنامج على مستوى التطبيق
        try:
            icon_path = resource_path(os.path.join('assets', 'icons.ico'))
            app_icon = QIcon(icon_path)
            app.setWindowIcon(app_icon)
        except Exception as e:
            print(f"Error setting application icon: {e}")

        # إنشاء جلسة قاعدة البيانات
        Session = scoped_session(sessionmaker(bind=engine))

        # تطبيق التصميم العصري الجديد
        try:
            style_path = resource_path(os.path.join('gui', 'modern_style.qss'))
            if not os.path.exists(style_path):
                style_path = resource_path(os.path.join('gui', 'style.qss'))
            with open(style_path, 'r', encoding='utf-8') as style_file:
                app.setStyleSheet(style_file.read())
        except FileNotFoundError:
            pass

        # --- منطق التشغيل الجديد ---
        if LICENSE_SYSTEM_AVAILABLE:
            if is_first_ever_run():
                print("🚀 التشغيل الأول للبرنامج...")
                from gui.activation_dialog import ActivationDialog
                from gui.initial_setup import InitialSetupDialog
                from license_manager import LicenseManager

                # 1. عرض نافذة معلومات التفعيل
                activation_dialog = ActivationDialog()
                if activation_dialog.exec_() != QDialog.Accepted:
                    print("❌ أغلق المستخدم نافذة التفعيل. إغلاق البرنامج.")
                    return 1

                # 2. إنشاء ترخيص تجريبي
                print("⏳ إنشاء ترخيص تجريبي لمدة 30 يوم...")
                license_manager = LicenseManager()
                license_manager.create_initial_license(days=30)
                print("✅ تم إنشاء الترخيص التجريبي.")

                # 3. عرض نافذة الإعداد الأولي للشركة (إلزامي)
                print("🏢 عرض نافذة الإعداد الأولي للشركة...")
                setup_dialog = InitialSetupDialog()
                if setup_dialog.exec_() != QDialog.Accepted:
                    print("❌ تم إلغاء الإعداد الأولي. إغلاق البرنامج.")
                    return 1
                print("✅ تم إكمال الإعداد الأولي للشركة.")

                # 4. مسح البيانات القديمة
                print('🧹 يتم مسح جميع البيانات القديمة (تثبيت جديد)...')
                clear_all_data(engine)
                print('✅ تم تفريغ جميع البيانات القديمة بنجاح!')

                # 5. عرض نافذة تسجيل الدخول للتشغيل الأول
                print("🔐 عرض نافذة تسجيل الدخول...")
                from gui.login import LoginDialog
                login_dialog = LoginDialog(engine)
                if login_dialog.exec_() != QDialog.Accepted:
                    print("❌ تم إلغاء تسجيل الدخول")
                    return 1

            else:
                # --- التشغيل العادي (ليس المرة الأولى) ---
                print("🔐 فحص الترخيص...")
                from license_ui import check_license_and_show_dialog
                if not check_license_and_show_dialog():
                    print("❌ لم يتم تفعيل الترخيص - إغلاق البرنامج")
                    # الرسالة ستظهر من داخل check_license_and_show_dialog
                    return 1
                print("✅ تم التحقق من الترخيص بنجاح")

                # فحص إذا كانت إعدادات الشركة موجودة (للحالات القديمة)
                from gui.initial_setup import check_initial_setup_needed, InitialSetupDialog
                if check_initial_setup_needed():
                    print("🏢 عرض نافذة الإعداد الأولي للشركة (تكميلي)...")
                    setup_dialog = InitialSetupDialog()
                    if setup_dialog.exec_() != QDialog.Accepted:
                        print("❌ تم إلغاء الإعداد الأولي")
                        return 1
                    print("✅ تم إكمال الإعداد الأولي للشركة")

                # عرض نافذة تسجيل الدخول للتشغيل العادي
                print("🔐 عرض نافذة تسجيل الدخول...")
                from gui.login import LoginDialog
                login_dialog = LoginDialog(engine)
                if login_dialog.exec_() != QDialog.Accepted:
                    print("❌ تم إلغاء تسجيل الدخول")
                    return 1

        else:
            print("⚠️ تحذير: نظام التراخيص غير متوفر - تشغيل البرنامج بدون حماية")
            # عرض نافذة تسجيل الدخول عند عدم توفر نظام التراخيص
            from gui.login import LoginDialog
            login_dialog = LoginDialog(engine)
            if login_dialog.exec_() != QDialog.Accepted:
                print("❌ تم إلغاء تسجيل الدخول")
                return 1

        # استيراد MainWindow هنا بعد تهيئة قاعدة البيانات
        from gui.main_window import MainWindow

        # تمرير المستخدم المسجل دخوله إلى النافذة الرئيسية
        user = None
        if hasattr(login_dialog, 'user') and login_dialog.user:
            user = login_dialog.user
            print(f"✅ تم تسجيل دخول المستخدم: {login_dialog.user.username}")

        window = MainWindow(engine=engine, user=user)
        window.show()
        print("✅ تم عرض النافذة الرئيسية بنجاح")

        return app.exec_()
    except Exception as e:
        QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء تشغيل البرنامج: {str(e)}")
        print(f"Error starting application: {e}")
        return 1
    finally:
        if 'Session' in locals():
            Session.remove()


if __name__ == "__main__":
    sys.exit(main())