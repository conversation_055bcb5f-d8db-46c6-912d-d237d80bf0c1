#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء مبسط وسريع لنظام المحاسبة العصري
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def print_simple_header():
    """طباعة رأس مبسط"""
    print("🚀 بناء سريع - نظام المحاسبة العصري v2.0")
    print("=" * 50)

def quick_build():
    """بناء سريع بدون اختبارات"""
    print("🔨 بناء سريع...")
    
    # أمر البناء المبسط
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name", "نظام_المحاسبة_العصري_v2_سريع",
        "main.py"
    ]
    
    try:
        print("⏳ جاري البناء...")
        subprocess.run(cmd, check=True)
        print("✅ تم البناء بنجاح!")
        
        # نسخ الملف إلى المجلد الحالي
        exe_path = "dist/نظام_المحاسبة_العصري_v2_سريع.exe"
        if os.path.exists(exe_path):
            shutil.copy2(exe_path, ".")
            print(f"✅ تم نسخ الملف التنفيذي")
            
            # إنشاء ملف تشغيل
            with open("تشغيل_سريع.bat", "w", encoding="utf-8") as f:
                f.write('@echo off\n')
                f.write('chcp 65001 > nul\n')
                f.write('echo 🚀 تشغيل نظام المحاسبة العصري...\n')
                f.write('start "" "نظام_المحاسبة_العصري_v2_سريع.exe"\n')
            
            print("✅ تم إنشاء ملف التشغيل السريع")
            return True
        else:
            print("❌ لم يتم العثور على الملف التنفيذي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في البناء: {e}")
        return False

def main():
    """الدالة الرئيسية للبناء السريع"""
    print_simple_header()
    
    # التحقق من PyInstaller
    try:
        subprocess.run([sys.executable, "-c", "import PyInstaller"], check=True)
    except:
        print("❌ PyInstaller غير مثبت")
        print("💡 قم بتثبيته: pip install pyinstaller")
        return False
    
    # البناء السريع
    if quick_build():
        print("\n🎉 البناء السريع مكتمل!")
        print("📁 الملفات الجاهزة:")
        print("   • نظام_المحاسبة_العصري_v2_سريع.exe")
        print("   • تشغيل_سريع.bat")
        print("\n🚀 جرب البرنامج الآن!")
        return True
    else:
        print("\n❌ فشل البناء السريع")
        return False

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
