from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QPushButton, QLineEdit, QComboBox, QLabel, QSpinBox,
                             QDoubleSpinBox, QTableWidgetItem, QMessageBox, QDialog,
                             QFrame, QGridLayout, QHeaderView, QDateEdit, QCompleter)
from PyQt5.QtCore import Qt, QDate, QStringListModel
from PyQt5.QtGui import QColor, QPixmap
from sqlalchemy.orm import Session
from sqlalchemy import or_
from database.models import Transaction, TransactionItem, Product, Supplier, TransactionType, ProductBarcode
from utils.new_design_invoice_printer import show_new_design_print_dialog
from gui.inventory import ProductDialog
from utils.dialog_utils import setup_medium_dialog
from utils.currency_formatter import format_number

class ProductSelectionDialog(QDialog):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        # إعداد النافذة مع خاصية التكبير
        setup_medium_dialog(self, "إضافة منتج للفاتورة", 800, 600, 1000, 800)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # منطقة البحث عن المنتج
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        search_layout = QHBoxLayout()
        search_frame.setLayout(search_layout)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث عن المنتج...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                font-size: 16px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
                min-width: 300px;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)
        search_layout.addWidget(self.search_input)
        layout.addWidget(search_frame)
        
        # قائمة المنتجات
        self.products_list = QTableWidget()
        self.products_list.setStyleSheet("""
            QTableWidget {
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                background-color: white;
                gridline-color: #DEE2E6;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #DEE2E6;
            }
            QHeaderView::section {
                background-color: #3498DB;
                color: white;
                padding: 15px;
                border: 1px solid #2980B9;
                font-weight: bold;
                font-size: 16px;
            }
            QTableWidget::item:selected {
                background-color: #E3F2FD;
                color: #1976D2;
            }
        """)
        
        self.products_list.setColumnCount(5)
        self.products_list.setHorizontalHeaderLabels([
            "الكود", "المنتج", "الفئة", "السعر", "المتوفر"
        ])
        
        # تحسين عرض الجدول
        header = self.products_list.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)    # الكود
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # الفئة
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # المتوفر
        
        # تحديد عرض الأعمدة
        header.resizeSection(0, 100)  # الكود
        header.resizeSection(2, 150)  # الفئة
        header.resizeSection(3, 120)  # السعر
        header.resizeSection(4, 120)  # المتوفر
        
        # تحديد ارتفاع الصفوف
        self.products_list.verticalHeader().setDefaultSectionSize(45)
        self.products_list.verticalHeader().setVisible(False)
        
        # منع تحرير الخلايا
        self.products_list.setEditTriggers(QTableWidget.NoEditTriggers)
        self.products_list.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.products_list)
        
        # منطقة تحديد الكمية والسعر
        details_frame = QFrame()
        details_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        details_layout = QGridLayout()
        details_frame.setLayout(details_layout)
        
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMaximum(1000)
        self.quantity_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                font-size: 14px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
                min-width: 100px;
            }
        """)
        
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setMaximum(1000000)
        self.price_spin.setDecimals(0)  # إزالة الخانات العشرية
        self.price_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 8px;
                font-size: 14px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
                min-width: 150px;
            }
        """)
        
        details_layout.addWidget(QLabel("الكمية:"), 0, 0)
        details_layout.addWidget(self.quantity_spin, 0, 1)
        details_layout.addWidget(QLabel("السعر:"), 0, 2)
        details_layout.addWidget(self.price_spin, 0, 3)
        
        layout.addWidget(details_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        confirm_btn = QPushButton("✅ إضافة للفاتورة")
        confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC3545;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #C82333;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
        
        # ربط الأحداث
        confirm_btn.clicked.connect(self.validate_and_accept)
        cancel_btn.clicked.connect(self.reject)
        self.search_input.textChanged.connect(self.search_products)
        self.products_list.itemSelectionChanged.connect(self.on_product_selected)
        
        # تحميل المنتجات
        self.load_products()
        
    def load_products(self):
        with Session(self.engine) as session:
            products = session.query(Product).all()
            self.products_list.setRowCount(len(products))
            
            for row, product in enumerate(products):
                # الكود
                self.products_list.setItem(row, 0, QTableWidgetItem(str(product.id)))
                
                # المنتج
                self.products_list.setItem(row, 1, QTableWidgetItem(product.name))
                
                # الفئة
                self.products_list.setItem(row, 2, QTableWidgetItem(product.category or "-"))
                
                # السعر
                self.products_list.setItem(row, 3, QTableWidgetItem(f"{product.purchase_price:,.0f}"))
                
                # المتوفر
                self.products_list.setItem(row, 4, QTableWidgetItem(str(product.quantity)))
                
    def search_products(self):
        search_text = self.search_input.text().strip()

        if not search_text:
            # إظهار جميع المنتجات
            for row in range(self.products_list.rowCount()):
                self.products_list.setRowHidden(row, False)
            return

        # البحث المحسن مع الباركودات المتعددة
        with Session(self.engine) as session:
            # البحث في الحقول الأساسية
            products_by_basic = session.query(Product).filter(
                or_(
                    Product.name.ilike(f"%{search_text}%"),
                    Product.code.ilike(f"%{search_text}%"),
                    Product.category.ilike(f"%{search_text}%"),
                    Product.barcode.ilike(f"%{search_text}%")
                )
            ).all()

            # البحث في الباركودات المتعددة
            products_by_barcode = session.query(Product).join(ProductBarcode).filter(
                ProductBarcode.barcode.ilike(f"%{search_text}%")
            ).all()

            # دمج النتائج
            found_product_ids = set()
            for product in products_by_basic + products_by_barcode:
                found_product_ids.add(product.id)

            # إخفاء/إظهار الصفوف حسب النتائج
            for row in range(self.products_list.rowCount()):
                product_id = int(self.products_list.item(row, 0).text())
                self.products_list.setRowHidden(row, product_id not in found_product_ids)
            
    def on_product_selected(self):
        selected_rows = self.products_list.selectedItems()
        if selected_rows:
            row = selected_rows[0].row()
            price = float(self.products_list.item(row, 3).text().replace(",", ""))
            self.price_spin.setValue(price)
            
    def validate_and_accept(self):
        selected_rows = self.products_list.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار منتج أولاً")
            return
            
        quantity = self.quantity_spin.value()
        if quantity <= 0:
            QMessageBox.warning(self, "تنبيه", "يجب أن تكون الكمية أكبر من صفر")
            return
            
        price = self.price_spin.value()
        if price <= 0:
            QMessageBox.warning(self, "تنبيه", "يجب أن يكون السعر أكبر من صفر")
            return
            
        self.accept()
            
    def get_selected_product(self):
        if self.exec_() == QDialog.Accepted:
            selected_rows = self.products_list.selectedItems()
            if selected_rows:
                row = selected_rows[0].row()
                product_id = int(self.products_list.item(row, 0).text())
                return product_id, self.quantity_spin.value(), self.price_spin.value()
        return None, None, None

class PurchasesWidget(QWidget):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.items = []
        self.setup_ui()

    def is_dark_mode(self):
        """التحقق من الوضع الليلي"""
        if hasattr(self.parent(), 'is_dark_mode'):
            return self.parent().is_dark_mode
        return False

    def get_table_style(self):
        """الحصول على تنسيق الجدول حسب الوضع"""
        if self.is_dark_mode():
            # ألوان الوضع الليلي
            return """
                QTableWidget {
                    border: 2px solid #334155;
                    background-color: #0F172A;
                    gridline-color: #334155;
                    font-size: 16px;
                    color: #F8FAFC;
                    border-radius: 12px;
                }
                QTableWidget::item {
                    padding: 12px;
                    border-bottom: 1px solid #334155;
                    color: #F8FAFC;
                }
                QTableWidget::item:selected {
                    background-color: #6366F1;
                    color: #F8FAFC;
                }
                QTableWidget::item:hover {
                    background-color: rgba(99, 102, 241, 0.1);
                }
                QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #F59E0B, stop:1 #FBBF24);
                    color: #F8FAFC;
                    padding: 15px;
                    border: none;
                    font-weight: bold;
                    font-size: 18px;
                    border-radius: 8px;
                }
                QHeaderView::section:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #D97706, stop:1 #F59E0B);
                }
            """
        else:
            # ألوان الوضع العادي
            return """
                QTableWidget {
                    border: 1px solid #DEE2E6;
                    background-color: white;
                    gridline-color: #DEE2E6;
                    font-size: 16px;
                    color: #212529;
                    border-radius: 8px;
                }
                QTableWidget::item {
                    padding: 12px;
                    border-bottom: 1px solid #DEE2E6;
                    color: #212529;
                }
                QTableWidget::item:selected {
                    background-color: #E3F2FD;
                    color: #1976D2;
                }
                QTableWidget::item:hover {
                    background-color: rgba(25, 118, 210, 0.1);
                }
                QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #FF9800, stop:1 #FFB74D);
                    color: white;
                    padding: 15px;
                    border: none;
                    font-weight: bold;
                    font-size: 18px;
                    border-radius: 8px;
                }
                QHeaderView::section:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #F57C00, stop:1 #FF9800);
                }
            """

    def get_row_colors(self, row):
        """الحصول على ألوان الصفوف حسب الوضع"""
        if self.is_dark_mode():
            return QColor("#1E293B") if row % 2 == 0 else QColor("#334155")
        else:
            return QColor("#F8F9FA") if row % 2 == 0 else QColor("#FFFFFF")

    def update_theme(self):
        """تحديث الألوان عند تغيير الوضع"""
        # تحديث تنسيق الجدول
        self.products_table.setStyleSheet(self.get_table_style())

        # إعادة تحميل البيانات لتطبيق الألوان الجديدة
        self.update_total()

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            with Session(self.engine) as session:
                suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()
                self.supplier_combo.clear()
                self.supplier_combo.addItem("اختر المورد", None)
                for supplier in suppliers:
                    self.supplier_combo.addItem(supplier.name, supplier.id)
        except Exception as e:
            print(f"Error loading suppliers: {e}")
            QMessageBox.warning(self, "تنبيه", "حدث خطأ أثناء تحميل قائمة الموردين")

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # Initialize supplier combo box first
        self.supplier_combo = QComboBox()
        self.supplier_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.9);
                padding: 12px;
                border: none;
                border-radius: 5px;
                min-width: 250px;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        
        # زر إضافة مورد جديد
        self.add_supplier_btn = QPushButton("➕ إضافة مورد")
        self.add_supplier_btn.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
                color: white;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        self.add_supplier_btn.clicked.connect(self.open_add_supplier_dialog)
        
        # إضافة خيار سريع في القائمة المنسدلة
        self.supplier_combo.addItem("➕ إضافة مورد جديد...", -1)
        self.supplier_combo.activated.connect(self.handle_supplier_combo)
        
        # Initialize paid amount field
        self.paid_amount = QDoubleSpinBox()
        self.paid_amount.setMaximum(1000000)
        self.paid_amount.setDecimals(0)  # إزالة الخانات العشرية
        self.paid_amount.valueChanged.connect(self.update_total)

        # Initialize discount and tax fields
        self.discount_spinbox = QDoubleSpinBox()
        self.discount_spinbox.setMaximum(100)
        self.discount_spinbox.setDecimals(0)  # إزالة الخانات العشرية
        self.discount_spinbox.setSuffix(" %")
        self.discount_spinbox.valueChanged.connect(self.update_total)

        self.tax_spinbox = QDoubleSpinBox()
        self.tax_spinbox.setMaximum(100)
        self.tax_spinbox.setDecimals(0)  # إزالة الخانات العشرية
        self.tax_spinbox.setValue(15)
        self.tax_spinbox.setSuffix(" %")
        self.tax_spinbox.valueChanged.connect(self.update_total)
        
        # Load suppliers after initializing the combo box
        self.load_suppliers()
        
        # تحسين العنوان والمعلومات الأساسية
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #F39C12;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 10px;
            }
            QLabel { color: #F39C12; }
            QComboBox {
                background-color: rgba(255, 255, 255, 0.9);
                padding: 8px;
                border: none;
                border-radius: 5px;
                min-width: 250px;
            }
        """)
        header_layout = QGridLayout()
        header_frame.setLayout(header_layout)

        # إضافة شعار المشتريات
        purchases_icon = QLabel()
        purchases_icon_pixmap = QPixmap("images/purchases.png")
        if not purchases_icon_pixmap.isNull():
            purchases_icon.setPixmap(purchases_icon_pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        
        # تحسين عنوان الفاتورة
        invoice_label = QLabel("فاتورة مشتريات جديدة")
        invoice_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #F39C12;")
        
        # إضافة رقم الفاتورة التلقائي
        self.invoice_number = QLabel()
        self.generate_invoice_number()
        self.invoice_number.setStyleSheet("font-size: 16px; color: #F39C12;")
        
        # إضافة التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setStyleSheet("""
            QDateEdit {
                background-color: rgba(255, 255, 255, 0.9);
                padding: 8px;
                border: none;
                border-radius: 5px;
            }
        """)
        
        header_layout.addWidget(purchases_icon, 0, 0)
        header_layout.addWidget(invoice_label, 0, 1)
        header_layout.addWidget(self.invoice_number, 0, 2)
        header_layout.addWidget(QLabel("المورد:"), 1, 0)
        header_layout.addWidget(self.supplier_combo, 1, 1)
        header_layout.addWidget(self.add_supplier_btn, 1, 2)
        header_layout.addWidget(QLabel("التاريخ:"), 1, 3)
        header_layout.addWidget(self.date_edit, 1, 4)
        
        layout.addWidget(header_frame)

        # إضافة شريط البحث السريع
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        search_layout = QHBoxLayout()
        search_frame.setLayout(search_layout)
        
        self.quick_search = QLineEdit()
        self.quick_search.setPlaceholderText("البحث السريع عن المنتجات...")
        self.quick_search.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 300px;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        # ربط الإكمال التلقائي
        self.setup_autocomplete()
        self.quick_search.textChanged.connect(self.on_search_text_changed)
        self.quick_search.returnPressed.connect(self.on_search_enter)
        
        self.scan_barcode = QPushButton("مسح الباركود")
        self.scan_barcode.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                min-width: 120px;
            }
        """)
        self.scan_barcode.clicked.connect(self.handle_barcode_scan)

        # زر إضافة منتج جديد
        self.add_product_btn = QPushButton("➕ إضافة منتج")
        self.add_product_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 14px;
                font-weight: bold;
                min-width: 140px;
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.add_product_btn.clicked.connect(self.add_new_product)

        search_layout.addWidget(self.quick_search)
        search_layout.addWidget(self.scan_barcode)
        search_layout.addWidget(self.add_product_btn)
        search_layout.addStretch()
        
        layout.addWidget(search_frame)

        # تحسين جدول المنتجات
        self.products_table = QTableWidget()
        # تطبيق التنسيق حسب الوضع
        self.products_table.setStyleSheet(self.get_table_style())
        self.products_table.setColumnCount(6)
        self.products_table.setHorizontalHeaderLabels([
            "المنتج", "الكمية",
            "السعر", "الإجمالي",
            "حذف", ""
        ])
        
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        for i in [1, 2, 3, 4, 5]:
            header.setSectionResizeMode(i, QHeaderView.Fixed)

        # تحديد عرض الأعمدة - توسيع مساحة السعر لاستيعاب الخط الكبير
        header.resizeSection(1, 120)  # الكمية
        header.resizeSection(2, 180)  # السعر - زيادة العرض لاستيعاب الخط الكبير
        header.resizeSection(3, 200)  # الإجمالي - زيادة العرض لاستيعاب الخط الكبير
        header.resizeSection(4, 100)  # حذف
        header.resizeSection(5, 100)  # عمود إضافي
        
        layout.addWidget(self.products_table)
        
        # تحسين ملخص الفاتورة
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 10px;
                padding: 20px;
            }
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
            }
            .total-label {
                font-size: 22px;
                font-weight: bold;
                color: #2C3E50;
            }
        """)
        
        summary_layout = QGridLayout()
        
        # المجاميع
        subtotal_label = QLabel("المجموع الفرعي:")
        self.subtotal_value = QLabel("0.00")
        
        discount_label = QLabel("الخصم:")
        self.discount_value = QLabel("0.00")
        
        tax_label = QLabel("الضريبة:")
        self.tax_value = QLabel("0.00")
        
        total_label = QLabel("الإجمالي النهائي:")
        total_label.setProperty("class", "total-label")
        self.total_value = QLabel("0.00")
        self.total_value.setProperty("class", "total-label")
        
        # طريقة الدفع
        self.payment_method = QComboBox()
        self.payment_method.addItems(["نقداً", "تحويل بنكي", "شيك"])
        
        summary_layout.addWidget(subtotal_label, 0, 0)
        summary_layout.addWidget(self.subtotal_value, 0, 1)
        summary_layout.addWidget(discount_label, 1, 0)
        summary_layout.addWidget(self.discount_value, 1, 1)
        summary_layout.addWidget(tax_label, 2, 0)
        summary_layout.addWidget(self.tax_value, 2, 1)
        summary_layout.addWidget(total_label, 3, 0)
        summary_layout.addWidget(self.total_value, 3, 1)
        summary_layout.addWidget(QLabel("طريقة الدفع:"), 0, 2)
        summary_layout.addWidget(self.payment_method, 0, 3)
        summary_layout.addWidget(QLabel("المدفوع:"), 1, 2)
        summary_layout.addWidget(self.paid_amount, 1, 3)
        
        summary_frame.setLayout(summary_layout)
        layout.addWidget(summary_frame)
        
        # أزرار التحكم النهائية
        buttons_layout = QHBoxLayout()
        
        save_print_btn = QPushButton("حفظ وطباعة")
        save_print_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                min-width: 140px;
                padding: 10px;
            }
        """)
        save_print_btn.clicked.connect(self.save_and_print)
        
        save_btn = QPushButton("حفظ الفاتورة")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #007BFF;
                min-width: 140px;
                padding: 10px;
            }
        """)
        save_btn.clicked.connect(self.save_invoice)
        
        clear_btn = QPushButton("فاتورة جديدة")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                min-width: 140px;
                padding: 10px;
            }
        """)
        clear_btn.clicked.connect(self.clear_invoice)
        
        buttons_layout.addWidget(clear_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(save_print_btn)
        
        layout.addLayout(buttons_layout)

    def generate_invoice_number(self):
        with Session(self.engine) as session:
            last_invoice = session.query(Transaction).filter(
                Transaction.type == TransactionType.PURCHASE
            ).order_by(Transaction.id.desc()).first()
            
            if last_invoice:
                new_number = last_invoice.id + 1
            else:
                new_number = 1
                
            self.invoice_number.setText(f"رقم الفاتورة: {new_number:06d}")

    def setup_autocomplete(self):
        """إعداد القائمة المنسدلة التلقائية للبحث"""
        self.update_autocomplete_list()
        self.completer = QCompleter()
        self.completer.setModel(QStringListModel())
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.completer.setFilterMode(Qt.MatchContains)
        self.completer.setMaxVisibleItems(15)
        self.completer.popup().setStyleSheet("""
            QListView {
                background-color: white;
                border: 2px solid #3498DB;
                border-radius: 8px;
                font-size: 16px;
                padding: 8px;
                selection-background-color: #3498DB;
                selection-color: white;
                min-height: 400px;
                max-height: 500px;
                min-width: 450px;
            }
            QListView::item {
                padding: 12px 15px;
                border-bottom: 1px solid #ECF0F1;
                min-height: 25px;
            }
            QListView::item:hover {
                background-color: #EBF3FD;
                border-radius: 4px;
            }
            QListView::item:selected {
                background-color: #3498DB;
                color: white;
                border-radius: 4px;
            }
        """)
        self.completer.popup().setMinimumSize(450, 400)
        self.completer.popup().setMaximumSize(600, 500)
        self.quick_search.setCompleter(self.completer)
        self.completer.activated.connect(self.on_autocomplete_selected)

    def update_autocomplete_list(self):
        """تحديث قائمة الإكمال التلقائي"""
        try:
            with Session(self.engine) as session:
                products = session.query(Product).all()
                product_list = []
                self.product_map = {}
                for product in products:
                    name_entry = product.name
                    product_list.append(name_entry)
                    self.product_map[name_entry] = product
                if hasattr(self, 'completer'):
                    self.completer.model().setStringList(product_list)
        except Exception as e:
            print(f"خطأ في تحديث قائمة الإكمال التلقائي: {e}")

    def on_search_text_changed(self, text):
        if len(text) >= 1:
            self.filter_autocomplete(text)

    def filter_autocomplete(self, text):
        try:
            with Session(self.engine) as session:
                products = session.query(Product).filter(
                    (Product.name.ilike(f"%{text}%")) |
                    (Product.code.ilike(f"%{text}%")) |
                    (Product.barcode.ilike(f"%{text}%"))
                ).limit(10).all()
                filtered_list = []
                self.product_map = {}
                for product in products:
                    name_entry = product.name
                    filtered_list.append(name_entry)
                    self.product_map[name_entry] = product
                self.completer.model().setStringList(filtered_list)
                self.adjust_popup_size(len(filtered_list))
        except Exception as e:
            print(f"خطأ في تصفية قائمة الإكمال التلقائي: {e}")

    def adjust_popup_size(self, item_count):
        try:
            popup = self.completer.popup()
            item_height = 37
            base_height = 50
            if item_count == 0:
                calculated_height = 100
            elif item_count <= 5:
                calculated_height = base_height + (item_count * item_height)
            elif item_count <= 10:
                calculated_height = base_height + (item_count * item_height)
            else:
                calculated_height = 500
            min_height = 150
            max_height = 500
            final_height = max(min_height, min(calculated_height, max_height))
            if item_count > 0:
                min_width = 450
                max_width = 700
                calculated_width = min_width + (item_count * 5)
                final_width = min(calculated_width, max_width)
            else:
                final_width = 450
            popup.setMinimumSize(final_width, final_height)
            popup.setMaximumSize(final_width + 50, final_height + 50)
            visible_items = min(15, max(5, item_count))
            self.completer.setMaxVisibleItems(visible_items)
        except Exception as e:
            print(f"خطأ في تحسين حجم القائمة المنسدلة: {e}")

    def on_search_enter(self):
        search_text = self.quick_search.text().strip()
        if search_text:
            self.search_and_add_product(search_text)

    def on_autocomplete_selected(self, text):
        if text in self.product_map:
            product = self.product_map[text]
            self.add_product_to_invoice(product)
            self.quick_search.clear()
            QMessageBox.information(
                self,
                "✅ تم الإضافة",
                f"تم إضافة '{product.name}' للفاتورة بنجاح!"
            )

    def search_and_add_product(self, search_text):
        try:
            with Session(self.engine) as session:
                product = session.query(Product).filter(
                    (Product.name.ilike(f"%{search_text}%")) |
                    (Product.code == search_text) |
                    (Product.barcode == search_text)
                ).first()
                if not product:
                    barcode_record = session.query(ProductBarcode).filter(
                        ProductBarcode.barcode == search_text
                    ).first()
                    if barcode_record:
                        product = barcode_record.product
                if product:
                    self.add_product_to_invoice(product)
                    self.quick_search.clear()
                    QMessageBox.information(
                        self,
                        "✅ تم الإضافة",
                        f"تم إضافة '{product.name}' للفاتورة بنجاح!"
                    )
                else:
                    QMessageBox.warning(
                        self,
                        "❌ منتج غير موجود",
                        f"لم يتم العثور على منتج يطابق: '{search_text}'"
                    )
        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ خطأ",
                f"حدث خطأ أثناء البحث:\n{str(e)}"
            )

    def handle_barcode_scan(self):
        # يمكن إضافة دعم لقارئ الباركود هنا
        pass

    def add_new_product(self):
        """فتح نافذة إضافة منتج جديد وإضافته للفاتورة تلقائياً"""
        try:
            # فتح نافذة إضافة منتج جديد
            dialog = ProductDialog(self.engine, parent=self)

            # إذا تم حفظ المنتج بنجاح
            if dialog.exec_() == QDialog.Accepted:
                # الحصول على ID المنتج الجديد من النافذة
                if hasattr(dialog, 'saved_product_id') and dialog.saved_product_id:
                    # تحميل المنتج الجديد من قاعدة البيانات
                    with Session(self.engine) as session:
                        new_product = session.query(Product).get(dialog.saved_product_id)
                        if new_product:
                            # إضافة المنتج للفاتورة مباشرة
                            self.add_product_to_table(new_product, 1, new_product.purchase_price)

                            # رسالة تأكيد
                            QMessageBox.information(
                                self,
                                "تم بنجاح",
                                f"تم إضافة المنتج '{new_product.name}' للمخزون وللفاتورة بنجاح!"
                            )
                        else:
                            QMessageBox.warning(self, "خطأ", "لم يتم العثور على المنتج المحفوظ")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة المنتج:\n{str(e)}")
            print(f"Error in add_new_product: {e}")

    def add_product_to_invoice(self, product):
        dialog = ProductSelectionDialog(self.engine)
        dialog.quantity_spin.setValue(1)
        dialog.price_spin.setValue(product.purchase_price)
        
        if dialog.exec_() == QDialog.Accepted:
            quantity = dialog.quantity_spin.value()
            price = dialog.price_spin.value()
            self.add_product_to_table(product, quantity, price)

    def add_product_to_table(self, product, quantity, price):
        row = self.products_table.rowCount()
        self.products_table.insertRow(row)
        
        # إضافة المنتج للجدول مع تنسيق خلفية الصف حسب الوضع
        bg_color = self.get_row_colors(row)
        
        self.products_table.setItem(row, 0, self.create_table_item(product.name, bg_color))
        self.products_table.setItem(row, 1, self.create_table_item(str(quantity), bg_color))
        self.products_table.setItem(row, 2, self.create_table_item(str(price), bg_color))
        
        total = price * quantity
        total_item = self.create_table_item(f"{total:,.0f}", bg_color)
        total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.products_table.setItem(row, 3, total_item)
        
        # زر الحذف
        delete_btn = QPushButton("حذف")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC3545;
                border: none;
                color: white;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #C82333;
            }
        """)
        delete_btn.clicked.connect(lambda: self.remove_product(row))
        self.products_table.setCellWidget(row, 4, delete_btn)
        
        self.items.append({
            'product_id': product.id,
            'quantity': quantity,
            'price': price
        })
        
        self.update_total()

    def create_table_item(self, text, bg_color):
        item = QTableWidgetItem(text)
        item.setBackground(bg_color)
        return item
                
    def remove_product(self, row):
        self.products_table.removeRow(row)
        self.items.pop(row)
        self.update_total()
        
    def update_total(self):
        subtotal = sum(item['price'] * item['quantity'] for item in self.items)
        discount = subtotal * (self.discount_spinbox.value() / 100)
        tax = (subtotal - discount) * (self.tax_spinbox.value() / 100)
        total = subtotal - discount + tax

        self.subtotal_value.setText(format_number(subtotal))
        self.discount_value.setText(format_number(discount))
        self.tax_value.setText(format_number(tax))
        self.total_value.setText(format_number(total))
        self.paid_amount.setMaximum(total)
        
    def save_invoice(self):
        if not self.items:
            QMessageBox.warning(self, "خطأ", "لا يوجد منتجات في الفاتورة")
            return None

        supplier_id = self.supplier_combo.currentData()
        if not supplier_id:
            QMessageBox.warning(self, "خطأ", "يجب اختيار المورد")
            return None
            
        total_amount = sum(item['price'] * item['quantity'] for item in self.items)
        paid_amount = self.paid_amount.value()
        
        try:
            with Session(self.engine) as session:
                # إنشاء الفاتورة
                transaction = Transaction(
                    type=TransactionType.PURCHASE,
                    total_amount=total_amount,
                    paid_amount=paid_amount,
                    supplier_id=supplier_id,
                    date=self.date_edit.date().toPyDate()
                )
                session.add(transaction)
                
                # إضافة المنتجات للفاتورة
                for item in self.items:
                    transaction_item = TransactionItem(
                        transaction=transaction,
                        product_id=item['product_id'],
                        quantity=item['quantity'],
                        price=item['price']
                    )
                    session.add(transaction_item)
                    
                    # تحديث المخزون
                    product = session.query(Product).get(item['product_id'])
                    product.quantity += item['quantity']
                    product.purchase_price = item['price']  # تحديث سعر الشراء
                    
                # تحديث رصيد المورد
                supplier = session.query(Supplier).get(supplier_id)
                supplier.balance += (total_amount - paid_amount)
                
                session.commit()

                # حفظ ID الفاتورة للإرجاع
                invoice_id = transaction.id

                QMessageBox.information(self, "نجاح", "تم حفظ الفاتورة بنجاح")
                self.clear_invoice()
                return invoice_id

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الفاتورة: {str(e)}")
            return None
            
    def save_and_print(self):
        """حفظ الفاتورة وطباعتها"""
        # حفظ الفاتورة أولاً
        invoice_id = self.save_invoice()

        # إذا تم الحفظ بنجاح، فتح نافذة الطباعة
        if invoice_id:
            try:
                print_invoice(self.engine, invoice_id, self)
            except Exception as e:
                QMessageBox.critical(self, "خطأ في الطباعة", f"تم حفظ الفاتورة بنجاح ولكن حدث خطأ أثناء الطباعة:\n{str(e)}")

    def print_invoice(self, invoice_id=None):
        """طباعة الفاتورة"""
        if invoice_id is None:
            QMessageBox.warning(self, "خطأ", "لا يوجد فاتورة محددة للطباعة")
            return

        try:
            show_new_design_print_dialog(self.engine, invoice_id, self)
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"حدث خطأ أثناء طباعة الفاتورة:\n{str(e)}")

    def clear_invoice(self):
        self.products_table.setRowCount(0)
        self.items.clear()
        self.paid_amount.setValue(0)
        self.discount_spinbox.setValue(0)
        self.quick_search.clear()
        self.generate_invoice_number()
        self.update_total()

    def handle_supplier_combo(self, index):
        if self.supplier_combo.itemData(index) == -1:
            self.open_add_supplier_dialog()

    def open_add_supplier_dialog(self):
        from gui.contacts import SupplierDialog
        dialog = SupplierDialog(self.engine, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_suppliers()
            # اختيار المورد الجديد تلقائياً
            if hasattr(dialog, 'saved_supplier_id') and dialog.saved_supplier_id:
                for i in range(self.supplier_combo.count()):
                    if self.supplier_combo.itemData(i) == dialog.saved_supplier_id:
                        self.supplier_combo.setCurrentIndex(i)
                        break