#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة التوزيع النهائية
"""

import os
import shutil
import json
from datetime import datetime

def create_final_package():
    """إنشاء حزمة التوزيع النهائية"""
    print("📦 إنشاء حزمة التوزيع النهائية...")
    
    # اسم مجلد التوزيع
    package_name = "نظام_المحاسبة_العصري_v2_النهائي"
    
    # إنشاء المجلد
    if os.path.exists(package_name):
        shutil.rmtree(package_name)
    os.makedirs(package_name)
    
    # نسخ الملف التنفيذي
    exe_files = [
        "dist/نظام_المحاسبة_العصري_v2_فوري.exe",
        "dist/نظام_المحاسبة_العصري_v2_نهائي.exe"
    ]
    
    exe_copied = False
    for exe_file in exe_files:
        if os.path.exists(exe_file):
            shutil.copy2(exe_file, os.path.join(package_name, "نظام_المحاسبة_العصري_v2.exe"))
            print(f"✅ نسخ {exe_file}")
            exe_copied = True
            break
    
    if not exe_copied:
        print("❌ لم يتم العثور على ملف تنفيذي!")
        return False
    
    # إنشاء ملف تشغيل
    batch_content = '''@echo off
chcp 65001 > nul
title نظام المحاسبة العصري v2.0 - النسخة النهائية
echo.
echo ========================================
echo    🏢 نظام المحاسبة العصري v2.0
echo    📁 مبني من main.py الأصلي
echo    ✅ جميع الإصلاحات مطبقة
echo ========================================
echo.
echo 🚀 جاري تشغيل البرنامج...
echo.

if exist "نظام_المحاسبة_العصري_v2.exe" (
    start "" "نظام_المحاسبة_العصري_v2.exe"
    echo ✅ تم تشغيل البرنامج بنجاح
    echo.
    echo 💡 الميزات الجديدة في v2.0:
    echo    - إصلاح شاشة التفعيل والإعدادات
    echo    - حذف الضريبة من المشتريات
    echo    - إضافة أزرار الطباعة
    echo    - عرض معلومات الترخيص
    echo    - تكامل إعدادات الشركة
    echo.
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 💡 تأكد من وجود الملف في نفس المجلد
    pause
)

timeout /t 5 > nul
'''
    
    with open(os.path.join(package_name, "تشغيل_البرنامج.bat"), "w", encoding="utf-8") as f:
        f.write(batch_content)
    print("✅ إنشاء ملف التشغيل")
    
    # إنشاء دليل المستخدم
    readme_content = f"""# 🏢 نظام المحاسبة العصري - الإصدار 2.0

## 📋 وصف البرنامج
نظام محاسبة شامل ومتطور لإدارة المبيعات والمشتريات والمخزون والعملاء.
تم بناؤه من main.py الأصلي مع جميع التصميمات والتقسيمات المطلوبة.

## ✨ الميزات الجديدة في الإصدار 2.0

### 🔧 الإصلاحات المطبقة:
✅ إصلاح شاشة التفعيل - نصوص واضحة ومساحات مريحة
✅ إصلاح شاشة إعدادات الشركة - حقول أكبر ونصوص واضحة  
✅ إصلاح مشكلة الواجهة الرئيسية بعد تسجيل الدخول
✅ إصلاح مشكلة الطباعة في فواتير المشتريات
✅ إصلاح مشكلة إنشاء ملف إعدادات الشركة

### 🆕 الميزات الجديدة:
🔑 عرض معلومات الترخيص ورقم الجهاز
🖨️ أزرار طباعة في صفحة عرض فواتير المشتريات
🏢 تكامل كامل لإعدادات الشركة في جميع أجزاء النظام
🗑️ حذف الضريبة من فواتير المشتريات
📋 نسخ تلقائي لمعلومات الترخيص

## 🚀 طريقة التشغيل
1. انقر نقراً مزدوجاً على "تشغيل_البرنامج.bat"
2. أو انقر نقراً مزدوجاً على "نظام_المحاسبة_العصري_v2.exe"
3. أدخل بيانات التفعيل أو اختر التخطي
4. أدخل إعدادات شركتك
5. ابدأ استخدام النظام

## 🔑 معلومات الترخيص
للحصول على معلومات الترخيص (رقم الجهاز وكود العميل):
- من قائمة "النظام" → "معلومات الترخيص"
- أو من الصفحة الرئيسية: زر "معلومات الترخيص"

## 📞 الدعم الفني
📧 البريد الإلكتروني: <EMAIL>
🌐 الموقع: www.sicocompany.com
📱 الواتساب: [رقم الدعم]

## 📅 تاريخ الإصدار
{datetime.now().strftime("%Y-%m-%d")}

## 🔧 تفاصيل تقنية
- الملف الرئيسي: main.py (مع التصميمات الأصلية)
- قاعدة البيانات: SQLite
- الواجهة: PyQt5
- نظام الترخيص: مدمج

© 2024 Sico Company - جميع الحقوق محفوظة
"""
    
    with open(os.path.join(package_name, "دليل_المستخدم.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    print("✅ إنشاء دليل المستخدم")
    
    # إنشاء معلومات الإصدار
    version_info = {
        "version": "2.0.0",
        "build_date": datetime.now().isoformat(),
        "main_file": "main.py",
        "description": "النسخة النهائية مع جميع الإصلاحات والتحسينات",
        "features": [
            "✅ مبني من main.py الأصلي مع التصميمات والتقسيمات المطلوبة",
            "✅ إصلاح شاشة التفعيل - نصوص واضحة ومساحات مريحة",
            "✅ إصلاح شاشة إعدادات الشركة - حقول أكبر ونصوص واضحة",
            "✅ إصلاح مشكلة الواجهة الرئيسية بعد تسجيل الدخول",
            "✅ حذف الضريبة من فواتير المشتريات",
            "✅ إصلاح مشكلة الطباعة في فواتير المشتريات",
            "✅ إضافة أزرار طباعة في صفحة عرض فواتير المشتريات",
            "✅ تكامل إعدادات الشركة في جميع أجزاء النظام",
            "✅ إصلاح مشكلة إنشاء ملف company_settings.json",
            "✅ إضافة ميزة عرض معلومات الترخيص ورقم الجهاز"
        ]
    }
    
    with open(os.path.join(package_name, "معلومات_الإصدار.json"), "w", encoding="utf-8") as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)
    print("✅ إنشاء معلومات الإصدار")
    
    # نسخ main.py كمرجع
    if os.path.exists("main.py"):
        shutil.copy2("main.py", os.path.join(package_name, "main_source.py"))
        print("✅ نسخ main.py كمرجع")
    
    print(f"\n✅ تم إنشاء حزمة التوزيع: {package_name}")
    return package_name

def main():
    """الدالة الرئيسية"""
    print("🎉 إنشاء حزمة التوزيع النهائية")
    print("=" * 50)
    
    package_name = create_final_package()
    
    if package_name:
        print("\n" + "=" * 50)
        print("🎊 تم إنشاء حزمة التوزيع بنجاح!")
        print("=" * 50)
        print(f"📁 اسم المجلد: {package_name}")
        print("📋 الملفات المتضمنة:")
        print("   • نظام_المحاسبة_العصري_v2.exe - الملف التنفيذي")
        print("   • تشغيل_البرنامج.bat - ملف التشغيل السريع")
        print("   • دليل_المستخدم.txt - دليل الاستخدام")
        print("   • معلومات_الإصدار.json - تفاصيل الإصدار")
        print("   • main_source.py - نسخة من main.py المستخدم")
        print("\n🚀 البرنامج جاهز للتوزيع والاختبار!")
        print("💡 تم البناء من main.py الأصلي مع جميع التصميمات")
        print("=" * 50)
    else:
        print("\n❌ فشل في إنشاء حزمة التوزيع!")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
