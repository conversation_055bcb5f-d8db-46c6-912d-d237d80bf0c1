"""
أدوات مساعدة للشاشات المنبثقة
"""

from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QDialog


def make_dialog_resizable(dialog, min_width=400, min_height=300, default_width=800, default_height=600):
    """
    جعل الشاشة المنبثقة قابلة للتكبير والتصغير
    
    Args:
        dialog: الشاشة المنبثقة (QDialog)
        min_width: الحد الأدنى للعرض
        min_height: الحد الأدنى للارتفاع
        default_width: العرض الافتراضي
        default_height: الارتفاع الافتراضي
    """
    # إضافة أزرار التحكم في النافذة (تكبير/تصغير/إغلاق)
    dialog.setWindowFlags(
        Qt.Dialog | 
        Qt.WindowMinMaxButtonsHint | 
        Qt.WindowCloseButtonHint |
        Qt.WindowSystemMenuHint
    )
    
    # تعيين الحد الأدنى للحجم
    dialog.setMinimumSize(min_width, min_height)
    
    # تعيين الحجم الافتراضي
    dialog.resize(default_width, default_height)


def apply_dialog_style(dialog, title="", modal=True):
    """
    تطبيق نمط موحد للشاشات المنبثقة
    
    Args:
        dialog: الشاشة المنبثقة (QDialog)
        title: عنوان النافذة
        modal: هل النافذة مودال أم لا
    """
    if title:
        dialog.setWindowTitle(title)
    
    dialog.setModal(modal)
    
    # تطبيق نمط CSS موحد
    dialog.setStyleSheet("""
        QDialog {
            background-color: #F8F9FA;
            color: #2C3E50;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }
        
        QLabel {
            color: #2C3E50;
            font-size: 21px;
            font-weight: bold;
            padding: 4px;
        }
        
        QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
            background-color: white;
            border: 2px solid #E9ECEF;
            border-radius: 6px;
            padding: 8px;
            font-size: 21px;
            font-weight: bold;
            color: #495057;
        }
        
        QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
            border-color: #3498DB;
            background-color: #EBF3FD;
        }
        
        QPushButton {
            background-color: #3498DB;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            font-size: 21px;
            font-weight: bold;
            min-width: 100px;
        }
        
        QPushButton:hover {
            background-color: #2980B9;
        }
        
        QPushButton:pressed {
            background-color: #21618C;
        }
        
        QPushButton:disabled {
            background-color: #BDC3C7;
            color: #7F8C8D;
        }
        
        QTableWidget {
            background-color: white;
            border: 2px solid #E9ECEF;
            border-radius: 6px;
            gridline-color: #DEE2E6;
            font-size: 13px;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #F1F3F4;
        }
        
        QTableWidget::item:selected {
            background-color: #3498DB;
            color: white;
        }
        
        QHeaderView::section {
            background-color: #34495E;
            color: white;
            font-weight: bold;
            padding: 10px;
            border: none;
            font-size: 13px;
        }
        
        QTabWidget::pane {
            border: 2px solid #E9ECEF;
            border-radius: 6px;
            background: white;
            margin-top: 5px;
        }
        
        QTabBar::tab {
            background: #F8F9FA;
            border: 2px solid #E9ECEF;
            padding: 10px 20px;
            margin-right: 2px;
            border-bottom: none;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            font-size: 14px;
            font-weight: bold;
        }
        
        QTabBar::tab:selected {
            background: white;
            border-bottom: 2px solid white;
            color: #3498DB;
        }
        
        QTabBar::tab:hover {
            background: #E9ECEF;
        }
        
        QFrame {
            border-radius: 6px;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #E9ECEF;
            border-radius: 6px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #2C3E50;
        }
        
        QCheckBox, QRadioButton {
            font-size: 14px;
            color: #2C3E50;
            spacing: 8px;
        }
        
        QCheckBox::indicator, QRadioButton::indicator {
            width: 18px;
            height: 18px;
        }
        
        QCheckBox::indicator:checked {
            background-color: #3498DB;
            border: 2px solid #3498DB;
            border-radius: 3px;
        }
        
        QCheckBox::indicator:unchecked {
            background-color: white;
            border: 2px solid #BDC3C7;
            border-radius: 3px;
        }
        
        QProgressBar {
            border: 2px solid #E9ECEF;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
            color: #2C3E50;
        }
        
        QProgressBar::chunk {
            background-color: #3498DB;
            border-radius: 4px;
        }
        
        QScrollBar:vertical {
            background: #F8F9FA;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background: #BDC3C7;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background: #95A5A6;
        }
        
        QScrollBar:horizontal {
            background: #F8F9FA;
            height: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:horizontal {
            background: #BDC3C7;
            border-radius: 6px;
            min-width: 20px;
        }
        
        QScrollBar::handle:horizontal:hover {
            background: #95A5A6;
        }
    """)


def setup_large_dialog(dialog, title="", min_width=800, min_height=600, default_width=1000, default_height=700):
    """
    إعداد شاشة منبثقة كبيرة مع جميع الخصائص
    
    Args:
        dialog: الشاشة المنبثقة (QDialog)
        title: عنوان النافذة
        min_width: الحد الأدنى للعرض
        min_height: الحد الأدنى للارتفاع
        default_width: العرض الافتراضي
        default_height: الارتفاع الافتراضي
    """
    make_dialog_resizable(dialog, min_width, min_height, default_width, default_height)
    apply_dialog_style(dialog, title, modal=True)


def setup_medium_dialog(dialog, title="", min_width=600, min_height=400, default_width=800, default_height=500):
    """
    إعداد شاشة منبثقة متوسطة مع جميع الخصائص
    """
    make_dialog_resizable(dialog, min_width, min_height, default_width, default_height)
    apply_dialog_style(dialog, title, modal=True)


def setup_small_dialog(dialog, title="", min_width=400, min_height=300, default_width=500, default_height=400):
    """
    إعداد شاشة منبثقة صغيرة مع جميع الخصائص
    """
    make_dialog_resizable(dialog, min_width, min_height, default_width, default_height)
    apply_dialog_style(dialog, title, modal=True)
